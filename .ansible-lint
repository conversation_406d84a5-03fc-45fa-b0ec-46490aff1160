---
parseable: true
skip_list:
  # see https://docs.ansible.com/ansible-lint/rules/default_rules.html for a list of all default rules

  # DO NOT add any other rules to this skip_list, instead use local `# noqa` with a comment explaining WHY it is necessary

  # These rules are intentionally skipped:
  #
  # [E204]: "Lines should be no longer than 160 chars"
  # This could be re-enabled with a major rewrite in the future.
  # For now, there's not enough value gain from strictly limiting line length.
  # (Disabled in May 2019)
  - '204'

  # [E701]: "meta/main.yml should contain relevant info"
  # Roles in Kubespray are not intended to be used/imported by Ansible Galaxy.
  # While it can be useful to have these metadata available, they are also available in the existing documentation.
  # (Disabled in May 2019)
  - '701'

  # [role-name] "meta/main.yml" Role name role-name does not match ``^+$`` pattern
  # Meta roles in Kubespray don't need proper names
  # (Disabled in June 2021)
  - 'role-name'

  # [var-naming] "defaults/main.yml" File defines variable 'apiVersion' that violates variable naming standards
  # In Kubespray we use variables that use camelCase to match their k8s counterparts
  # (Disabled in June 2021)
  - 'var-naming'
  
  # [fqcn-builtins]
  # Roles in kubespray don't need fully qualified collection names
  - 'fqcn-builtins'
warn_list:
  - command-instead-of-module  # Using command rather than module.
  - command-instead-of-shell  # Use shell only when shell functionality is required.
  - deprecated-command-syntax  # Using command rather than an argument to e.g. file.
  - experimental  # all rules tagged as experimental
  - jinja[spacing]  # Rule that looks inside jinja2 templates.
  - name[missing]  # Rule for checking task and play names.
  - name[template]  # Rule for checking task and play names.
  - name[play]  # Rule for checking task and play names.
  - no-changed-when  # Commands should not change things if nothing needs doing.
  - risky-shell-pipe  # Shells that use pipes should set the pipefail option.
  - yaml[line-length]  # Violations reported by yamllint.
  - yaml[new-line-at-end-of-file]  # Violations reported by yamllint.
  - yaml[trailing-spaces]  # Violations reported by yamllint.
  - yaml[truthy]  # Violations reported by yamllint.
  - ignore-errors # Use failed_when and specify error conditions instead of using ignore_errors.
exclude_paths:
  - playbooks/disable-firewalld.yml
  - playbooks/config-for-kube-vip.yml
  - playbooks/mount-xfs-pquota.yml
  - playbooks/ensure-kube-resolv-conf.yml
