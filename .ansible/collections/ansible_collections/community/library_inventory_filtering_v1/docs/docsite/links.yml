---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

edit_on_github:
  repository: ansible-collections/community.library_inventory_filtering
  branch: stable-1
  path_prefix: ''

extra_links:
  - description: Submit a bug report
    url: https://github.com/ansible-collections/community.library_inventory_filtering/issues/new?assignees=&labels=&template=bug_report.md
  - description: Request a feature
    url: https://github.com/ansible-collections/community.library_inventory_filtering/issues/new?assignees=&labels=&template=feature_request.md

communication:
  matrix_rooms:
    - topic: General usage and support questions
      room: '#community:ansible.im'
  forums:
    - topic: "Ansible Forum: Discussions about community.library_inventory_filtering_vX"
      # The following URL directly points to the "inventory-filter-dev" tag
      url: https://forum.ansible.com/tag/inventory-filter-dev
    - topic: "Ansible Forum: Discussions on collection development"
      # The following URL directly points to the "Collection Development" category
      url: https://forum.ansible.com/c/project/collection-development/27
