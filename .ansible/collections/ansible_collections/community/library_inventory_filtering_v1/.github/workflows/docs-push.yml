---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

name: Collection Docs
concurrency:
  group: docs-push-${{ github.sha }}
  cancel-in-progress: true
"on":
  push:
    branches:
      - stable-*
    tags:
      - '*'
  # Run CI once per day (at 04:45 UTC)
  schedule:
    - cron: '45 4 * * *'
  # Allow manual trigger (for newer antsibull-docs, sphinx-ansible-theme, ... versions)
  workflow_dispatch:

jobs:
  build-docs:
    permissions:
      contents: read
    name: Build Ansible Docs
    uses: ansible-community/github-docs-build/.github/workflows/_shared-docs-build-push.yml@main
    with:
      collection-name: community.library_inventory_filtering_v1
      init-lenient: false
      init-fail-on-error: true
      squash-hierarchy: true
      init-project: Community.Library_Inventory_Filtering Collection
      init-copyright: Community.Library_Inventory_Filtering Contributors
      init-title: Community.Library_Inventory_Filtering Collection Documentation
      init-html-short-title: Community.Library_Inventory_Filtering Collection Docs
      init-extra-html-theme-options: |
        documentation_home_url=https://${{ github.repository_owner }}.github.io/${{ github.event.repository.name }}/branch/stable-1/

  publish-docs-gh-pages:
    # for now we won't run this on forks
    if: github.repository == 'ansible-collections/community.library_inventory_filtering'
    permissions:
      contents: write
      pages: write
      id-token: write
    needs: [build-docs]
    name: Publish Ansible Docs
    uses: ansible-community/github-docs-build/.github/workflows/_shared-docs-build-publish-gh-pages.yml@main
    with:
      artifact-name: ${{ needs.build-docs.outputs.artifact-name }}
      publish-gh-pages-branch: true
    secrets:
      GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
