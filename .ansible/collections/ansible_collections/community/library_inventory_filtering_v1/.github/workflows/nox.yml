---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

name: nox
'on':
  push:
    branches:
      - main
      - stable-*
  pull_request:
  # Run CI once per day (at 04:30 UTC)
  schedule:
    - cron: '30 4 * * *'
  workflow_dispatch:

jobs:
  nox:
    runs-on: ubuntu-latest
    name: "Run extra sanity tests"
    steps:
      - name: Check out collection
        uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: Run nox
        uses: ansible-community/antsibull-nox@main

  ansible-test:
    uses: ansible-community/antsibull-nox/.github/workflows/reusable-nox-matrix.yml@main
    with:
      upload-codecov: true
    secrets:
      CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
