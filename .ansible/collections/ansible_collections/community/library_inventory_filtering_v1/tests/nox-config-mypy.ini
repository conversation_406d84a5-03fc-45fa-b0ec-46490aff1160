# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

[mypy]
namespace_packages = True
explicit_package_bases = True

# strict = True -- only try to enable once everything is typed
strict_equality = True

[mypy-ansible.*]
# ansible-core has no typing information
ignore_missing_imports = True

[mypy-ansible_collections.community.internal_test_tools.*]
# community.internal_test_tools has no typing information
ignore_missing_imports = True

[mypy-ansible_collections.community.internal_test_tools.tests.unit.compat.mock]
ignore_errors = True
