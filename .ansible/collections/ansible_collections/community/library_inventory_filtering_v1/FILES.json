{"files": [{"name": ".", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": ".github", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": ".github/workflows", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": ".github/workflows/docs-pr.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "b6183d5eeb01ed9c3c4ddc45196565e48ed96bcd44f2eb09219a772b9f328fd6", "format": 1}, {"name": ".github/workflows/docs-push.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "7fcfebb1e93c0ea7e0fb16952db2e7989999075e7d4c60f66d6975f50aa91c33", "format": 1}, {"name": ".github/workflows/nox.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "bc36534fff0bfca2891dadc5b67f181a809976ca6666f1bcbc92f5706b4b7c7b", "format": 1}, {"name": ".github/dependabot.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "673731e08adb20eb69f8b1a19a3ecb6b1989ac7d8f8e9a5b477aaeb480dffa1f", "format": 1}, {"name": "LICENSES", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "LICENSES/GPL-3.0-or-later.txt", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "3972dc9744f6499f0f9b2dbf76696f2ae7ad8af9b23dde66d6af86c9dfb36986", "format": 1}, {"name": "changelogs", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "changelogs/fragments", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "changelogs/fragments/.keep", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "format": 1}, {"name": "changelogs/changelog.yaml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "842eacaa216acb05396a1273c22e73eb9790394a8c28ae2b71bf54e347ab08ac", "format": 1}, {"name": "changelogs/changelog.yaml.license", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "6eb915239f9f35407fa68fdc41ed6522f1fdcce11badbdcd6057548023179ac1", "format": 1}, {"name": "changelogs/config.yaml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "7d6c99b4dcebcd23af07bf2d0273fb8aa2b22f2fd7d607dd1cd0f637cf30e945", "format": 1}, {"name": "docs", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "docs/docsite", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "docs/docsite/rst", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "docs/docsite/rst/usage_guide.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "9cb7a0386071576cb076b0ac3bd680df3c3a1c22d316587c445666dadba986f3", "format": 1}, {"name": "docs/docsite/config.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "0c5ec9ff76cf4db33b5d3f771419ef50d448e5d510cb7a98fc07dd9ecee69c4e", "format": 1}, {"name": "docs/docsite/extra-docs.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "bbc2aa66c91ee37c4aea3e5aaff25983305e48b7281dc0356506ccb1e7c05103", "format": 1}, {"name": "docs/docsite/links.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "0a829c75255705df6a223ca35ca0933e31d0013aefb2bfd5119d3c4bfbc4bb53", "format": 1}, {"name": "meta", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "meta/runtime.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "4ed63a88ab6b7c15fc1ba0c16d2accb77b3305fff800731770f40609a7593393", "format": 1}, {"name": "plugins", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "plugins/doc_fragments", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "plugins/doc_fragments/inventory_filter.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "e4740aab4f14ef07067ea358d989ef629a1c7540e086ce9b5ec67c6522d48a16", "format": 1}, {"name": "plugins/plugin_utils", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "plugins/plugin_utils/inventory_filter.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "581f3126622439f94829dff3e35e68847e4a946e64fb86c3e68f756214d4b58d", "format": 1}, {"name": "plugins/plugin_utils/inventory_filter.pyi", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "9577eed941c5c8536d85721b2a98a9f8dd7c6c78cfb32d1185935c61cfaa3c26", "format": 1}, {"name": "plugins/plugin_utils/py.typed", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "format": 1}, {"name": "tests", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/sanity", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/sanity/ignore-2.10.txt", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "db04ae3104fec956b77998c33fb0b449089abeb74f3b92bc3c3cb4a4f26e793a", "format": 1}, {"name": "tests/sanity/ignore-2.10.txt.license", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "6eb915239f9f35407fa68fdc41ed6522f1fdcce11badbdcd6057548023179ac1", "format": 1}, {"name": "tests/sanity/ignore-2.11.txt", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "db04ae3104fec956b77998c33fb0b449089abeb74f3b92bc3c3cb4a4f26e793a", "format": 1}, {"name": "tests/sanity/ignore-2.11.txt.license", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "6eb915239f9f35407fa68fdc41ed6522f1fdcce11badbdcd6057548023179ac1", "format": 1}, {"name": "tests/sanity/ignore-2.9.txt", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "db04ae3104fec956b77998c33fb0b449089abeb74f3b92bc3c3cb4a4f26e793a", "format": 1}, {"name": "tests/sanity/ignore-2.9.txt.license", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "6eb915239f9f35407fa68fdc41ed6522f1fdcce11badbdcd6057548023179ac1", "format": 1}, {"name": "tests/unit", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/unit/plugins", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/unit/plugins/plugin_utils", "ftype": "dir", "chksum_type": null, "chksum_sha256": null, "format": 1}, {"name": "tests/unit/plugins/plugin_utils/test_inventory_filter.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "8d1f2facc23f631bf008317c7dfde393a24b798d5be60c14d5a35ab3c3fbd085", "format": 1}, {"name": "tests/unit/requirements.txt", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "dbf3688bcda2816222b33ee535d6513ddc5c7814bcf91d9aefb85911b49f344d", "format": 1}, {"name": "tests/unit/requirements.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "8215462f19d64158eda54059f4c93db5bf8502dde2dbf6d995c7bd3b9023419d", "format": 1}, {"name": "tests/config.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "498d46cf08b5abb09880088fa8fd142315f7f775e94cdc7d41364ed20fc2cd65", "format": 1}, {"name": "tests/nox-config-black.toml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "c47eb3d4eed7387b320f0ce71db958f5b78ef803a8aa27ea010803c878aee5f7", "format": 1}, {"name": "tests/nox-config-flake8.ini", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "06728728f05fcb33be74f5fca84fcd968f08267e36ec06a1bfd36eb09b51bd66", "format": 1}, {"name": "tests/nox-config-isort.cfg", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "cc8ed708c29fc32307541663354f45ab4fac68ec6ddb6e2c8b5b9598fdf0f195", "format": 1}, {"name": "tests/nox-config-mypy.ini", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "ef162d2bbbbc44be6310688fd5e38108a094e5bfc055db27ddfbfcccaa8e43a5", "format": 1}, {"name": "tests/nox-config-pylint.rc", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "e7238a93f0af6c60526f550821edc5359f6e1c54d9589ba5bf8535f7ec187705", "format": 1}, {"name": "tests/nox-config-yamllint.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "aaf89caba9edfcc9161533e6009df0ddbcdd56f75340dcb01a6dc0fb37d32d2a", "format": 1}, {"name": ".git-blame-ignore-revs", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "9146c0de369aba9ce2f427fc1ba8690892576884bad723f30189154b6fc22bc0", "format": 1}, {"name": "CHANGELOG.md", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "72d6254fbcc289ab1f4a5424577314524cdbfdb2bf934e579f58001fb392725b", "format": 1}, {"name": "CHANGELOG.md.license", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "6eb915239f9f35407fa68fdc41ed6522f1fdcce11badbdcd6057548023179ac1", "format": 1}, {"name": "CHANGELOG.rst", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "8fff05bbd4bbe5fae468ab295b533890af69f05895e4883c48c7d27a2b92e74c", "format": 1}, {"name": "CHANGELOG.rst.license", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "6eb915239f9f35407fa68fdc41ed6522f1fdcce11badbdcd6057548023179ac1", "format": 1}, {"name": "COPYING", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "3972dc9744f6499f0f9b2dbf76696f2ae7ad8af9b23dde66d6af86c9dfb36986", "format": 1}, {"name": "README.md", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "fd6af6ddf04b9bb191ab337e121cbc182664f663cbebf7f4322ae5d9ff3a0473", "format": 1}, {"name": "REUSE.toml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "646b5ce1ce1d31715c0e6a8ad9c2cc2828973e92badb73d1d6e7be33a3d2af3f", "format": 1}, {"name": "antsibull-nox.toml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "c6cb8bef1bcdc62ac17ba46cec809ddb2a7a45db1cf4f5321d137455c95b6a64", "format": 1}, {"name": "codecov.yml", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "fccd5fc7ac924bcb9b7269c4ecd7c0fa7f25ed9fa1a88ced6f06f933a4f9b391", "format": 1}, {"name": "noxfile.py", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "28e31f1fb7a5bac30cff004209402ac5150df7feab6c3bf708ff7c29e94c54d7", "format": 1}], "format": 1}