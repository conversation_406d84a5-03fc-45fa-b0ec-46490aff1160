# Community Inventory Filtering Library Collection Release Notes

**Topics**

- <a href="#v1-1-1">v1\.1\.1</a>
    - <a href="#release-summary">Release Summary</a>
- <a href="#v1-1-0">v1\.1\.0</a>
    - <a href="#release-summary-1">Release Summary</a>
    - <a href="#minor-changes">Minor Changes</a>
    - <a href="#bugfixes">Bugfixes</a>
- <a href="#v1-0-2">v1\.0\.2</a>
    - <a href="#release-summary-2">Release Summary</a>
- <a href="#v1-0-1">v1\.0\.1</a>
    - <a href="#release-summary-3">Release Summary</a>
- <a href="#v1-0-0">v1\.0\.0</a>
    - <a href="#release-summary-4">Release Summary</a>
- <a href="#v0-1-0">v0\.1\.0</a>
    - <a href="#release-summary-5">Release Summary</a>

<a id="v1-1-1"></a>
## v1\.1\.1

<a id="release-summary"></a>
### Release Summary

Maintenance release\.

<a id="v1-1-0"></a>
## v1\.1\.0

<a id="release-summary-1"></a>
### Release Summary

Feature\, bugfix\, and maintenance release with support for Data Tagging\.

<a id="minor-changes"></a>
### Minor Changes

* Add typing information for the <code>inventory\_filter</code> plugin utils \([https\://github\.com/ansible\-collections/community\.library\_inventory\_filtering/pull/22](https\://github\.com/ansible\-collections/community\.library\_inventory\_filtering/pull/22)\)\.

<a id="bugfixes"></a>
### Bugfixes

* inventory\_filter plugin utils \- make compatible with ansible\-core\'s Data Tagging feature \([https\://github\.com/ansible\-collections/community\.library\_inventory\_filtering/pull/24](https\://github\.com/ansible\-collections/community\.library\_inventory\_filtering/pull/24)\)\.
* inventory\_plugin plugin util \- <code>parse\_filters</code> now filters <code>None</code> values with allowed keys \([https\://github\.com/ansible\-collections/community\.library\_inventory\_filtering/pull/27](https\://github\.com/ansible\-collections/community\.library\_inventory\_filtering/pull/27)\)\.

<a id="v1-0-2"></a>
## v1\.0\.2

<a id="release-summary-2"></a>
### Release Summary

Maintenance release with updated links\.

<a id="v1-0-1"></a>
## v1\.0\.1

<a id="release-summary-3"></a>
### Release Summary

Maintenance release with documentation\.

<a id="v1-0-0"></a>
## v1\.0\.0

<a id="release-summary-4"></a>
### Release Summary

First production ready release\.

<a id="v0-1-0"></a>
## v0\.1\.0

<a id="release-summary-5"></a>
### Release Summary

Initial test release\.
