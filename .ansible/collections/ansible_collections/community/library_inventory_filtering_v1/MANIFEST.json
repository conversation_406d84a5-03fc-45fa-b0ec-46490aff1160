{"collection_info": {"namespace": "community", "name": "library_inventory_filtering_v1", "version": "1.1.1", "authors": ["<PERSON> (github.com/felixfontein)"], "readme": "README.md", "tags": ["inventory", "filtering"], "description": "Library for inventory plugins in other collections that allows to filter hosts in a generic way.", "license": ["GPL-3.0-or-later"], "license_file": null, "dependencies": {}, "repository": "https://github.com/ansible-collections/community.library_inventory_filtering", "documentation": null, "homepage": "https://github.com/ansible-collections/community.library_inventory_filtering", "issues": "https://github.com/ansible-collections/community.library_inventory_filtering/issues"}, "file_manifest_file": {"name": "FILES.json", "ftype": "file", "chksum_type": "sha256", "chksum_sha256": "9252f1d1e6bbfbba56637e7f8abf0003b5030414e980e7669931716553420d4a", "format": 1}, "format": 1}