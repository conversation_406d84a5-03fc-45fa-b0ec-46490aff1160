---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- debug:
    msg: Running tests/options.yml

- name: Create a Swarm cluster
  docker_swarm:
    state: present
    advertise_addr: "{{ansible_default_ipv4.address | default('127.0.0.1')}}"
    name: default
  diff: true

####################################################################
## autolock_managers ###############################################
####################################################################

- name: autolock_managers (check mode)
  docker_swarm:
    state: present
    autolock_managers: true
  check_mode: true
  diff: true
  register: output_1
  ignore_errors: true

- name: autolock_managers
  docker_swarm:
    state: present
    autolock_managers: true
  diff: true
  register: output_2
  ignore_errors: true

- name: autolock_managers (idempotent)
  docker_swarm:
    state: present
    autolock_managers: true
  diff: true
  register: output_3
  ignore_errors: true

- name: autolock_managers (idempotent, check mode)
  docker_swarm:
    state: present
    autolock_managers: true
  check_mode: true
  diff: true
  register: output_4
  ignore_errors: true

- name: autolock_managers (change, check mode)
  docker_swarm:
    state: present
    autolock_managers: false
  check_mode: true
  diff: true
  register: output_5
  ignore_errors: true

- name: autolock_managers (change)
  docker_swarm:
    state: present
    autolock_managers: false
  diff: true
  register: output_6
  ignore_errors: true

- name: autolock_managers (force new swarm)
  docker_swarm:
    state: present
    force: true
    advertise_addr: "{{ansible_default_ipv4.address | default('127.0.0.1')}}"
    autolock_managers: true
  diff: true
  register: output_7
  ignore_errors: true

- name: assert autolock_managers changes
  assert:
    that:
      - 'output_1 is changed'
      - 'output_1.actions[0] == "Swarm cluster updated"'
      - 'output_1.diff.before is defined'
      - 'output_1.diff.after is defined'
      - 'output_2 is changed'
      - 'output_2.actions[0] == "Swarm cluster updated"'
      - 'output_2.diff.before is defined'
      - 'output_2.diff.after is defined'
      - 'output_3 is not changed'
      - 'output_3.actions[0] == "No modification"'
      - 'output_3.diff.before is defined'
      - 'output_3.diff.after is defined'
      - 'output_4 is not changed'
      - 'output_4.actions[0] == "No modification"'
      - 'output_4.diff.before is defined'
      - 'output_4.diff.after is defined'
      - 'output_5 is changed'
      - 'output_5.actions[0] == "Swarm cluster updated"'
      - 'output_5.diff.before is defined'
      - 'output_5.diff.after is defined'
      - 'output_6 is changed'
      - 'output_6.actions[0] == "Swarm cluster updated"'
      - 'output_6.diff.before is defined'
      - 'output_6.diff.after is defined'
  when: docker_py_version is version('2.6.0', '>=')

- name: assert UnlockKey in swarm_facts
  assert:
    that:
      - 'output_2.swarm_facts.UnlockKey is truthy'
      - 'output_3.swarm_facts.UnlockKey is none'
      - 'output_6.swarm_facts.UnlockKey is none'
      - 'output_7.swarm_facts.UnlockKey is truthy'
  when: docker_py_version is version('2.7.0', '>=')

- assert:
    that:
      - output_1 is failed
      - "('version is ' ~ docker_py_version ~ ' ') in output_1.msg"
      - "'Minimum version required is 2.6.0 ' in output_1.msg"
  when: docker_py_version is version('2.6.0', '<')

####################################################################
## ca_force_rotate #################################################
####################################################################

- name: ca_force_rotate (check mode)
  docker_swarm:
    state: present
    ca_force_rotate: 1
  check_mode: true
  diff: true
  register: output_1
  ignore_errors: true

- name: ca_force_rotate
  docker_swarm:
    state: present
    ca_force_rotate: 1
  diff: true
  register: output_2
  ignore_errors: true

- name: ca_force_rotate (idempotent)
  docker_swarm:
    state: present
    ca_force_rotate: 1
  diff: true
  register: output_3
  ignore_errors: true

- name: ca_force_rotate (idempotent, check mode)
  docker_swarm:
    state: present
    ca_force_rotate: 1
  check_mode: true
  diff: true
  register: output_4
  ignore_errors: true

- name: ca_force_rotate (change, check mode)
  docker_swarm:
    state: present
    ca_force_rotate: 0
  check_mode: true
  diff: true
  register: output_5
  ignore_errors: true

- name: ca_force_rotate (change)
  docker_swarm:
    state: present
    ca_force_rotate: 0
  diff: true
  register: output_6
  ignore_errors: true

- name: assert ca_force_rotate changes
  assert:
    that:
      - 'output_1 is changed'
      - 'output_1.actions[0] == "Swarm cluster updated"'
      - 'output_1.diff.before is defined'
      - 'output_1.diff.after is defined'
      - 'output_2 is changed'
      - 'output_2.actions[0] == "Swarm cluster updated"'
      - 'output_2.diff.before is defined'
      - 'output_2.diff.after is defined'
      - 'output_3 is not changed'
      - 'output_3.actions[0] == "No modification"'
      - 'output_3.diff.before is defined'
      - 'output_3.diff.after is defined'
      - 'output_4 is not changed'
      - 'output_4.actions[0] == "No modification"'
      - 'output_4.diff.before is defined'
      - 'output_4.diff.after is defined'
      - 'output_5 is changed'
      - 'output_5.actions[0] == "Swarm cluster updated"'
      - 'output_5.diff.before is defined'
      - 'output_5.diff.after is defined'
      - 'output_6 is changed'
      - 'output_6.actions[0] == "Swarm cluster updated"'
      - 'output_6.diff.before is defined'
      - 'output_6.diff.after is defined'
  when: docker_py_version is version('2.6.0', '>=')
- assert:
    that:
      - output_1 is failed
      - "('version is ' ~ docker_py_version ~ ' ') in output_1.msg"
      - "'Minimum version required is 2.6.0 ' in output_1.msg"
  when: docker_py_version is version('2.6.0', '<')

####################################################################
## dispatcher_heartbeat_period #####################################
####################################################################

- name: dispatcher_heartbeat_period (check mode)
  docker_swarm:
    state: present
    dispatcher_heartbeat_period: 10
  check_mode: true
  diff: true
  register: output_1

- name: dispatcher_heartbeat_period
  docker_swarm:
    state: present
    dispatcher_heartbeat_period: 10
  diff: true
  register: output_2

- name: dispatcher_heartbeat_period (idempotent)
  docker_swarm:
    state: present
    dispatcher_heartbeat_period: 10
  diff: true
  register: output_3

- name: dispatcher_heartbeat_period (idempotent, check mode)
  docker_swarm:
    state: present
    dispatcher_heartbeat_period: 10
  check_mode: true
  diff: true
  register: output_4

- name: dispatcher_heartbeat_period (change, check mode)
  docker_swarm:
    state: present
    dispatcher_heartbeat_period: 23
  check_mode: true
  diff: true
  register: output_5

- name: dispatcher_heartbeat_period (change)
  docker_swarm:
    state: present
    dispatcher_heartbeat_period: 23
  diff: true
  register: output_6

- name: assert dispatcher_heartbeat_period changes
  assert:
    that:
      - 'output_1 is changed'
      - 'output_1.actions[0] == "Swarm cluster updated"'
      - 'output_1.diff.before is defined'
      - 'output_1.diff.after is defined'
      - 'output_2 is changed'
      - 'output_2.actions[0] == "Swarm cluster updated"'
      - 'output_2.diff.before is defined'
      - 'output_2.diff.after is defined'
      - 'output_3 is not changed'
      - 'output_3.actions[0] == "No modification"'
      - 'output_3.diff.before is defined'
      - 'output_3.diff.after is defined'
      - 'output_4 is not changed'
      - 'output_4.actions[0] == "No modification"'
      - 'output_4.diff.before is defined'
      - 'output_4.diff.after is defined'
      - 'output_5 is changed'
      - 'output_5.actions[0] == "Swarm cluster updated"'
      - 'output_5.diff.before is defined'
      - 'output_5.diff.after is defined'
      - 'output_6 is changed'
      - 'output_6.actions[0] == "Swarm cluster updated"'
      - 'output_6.diff.before is defined'
      - 'output_6.diff.after is defined'

####################################################################
## election_tick ###################################################
####################################################################

- name: election_tick (check mode)
  docker_swarm:
    state: present
    election_tick: 20
  check_mode: true
  diff: true
  register: output_1

- name: election_tick
  docker_swarm:
    state: present
    election_tick: 20
  diff: true
  register: output_2

- name: election_tick (idempotent)
  docker_swarm:
    state: present
    election_tick: 20
  diff: true
  register: output_3

- name: election_tick (idempotent, check mode)
  docker_swarm:
    state: present
    election_tick: 20
  check_mode: true
  diff: true
  register: output_4

- name: election_tick (change, check mode)
  docker_swarm:
    state: present
    election_tick: 5
  check_mode: true
  diff: true
  register: output_5

- name: election_tick (change)
  docker_swarm:
    state: present
    election_tick: 5
  diff: true
  register: output_6

- name: assert election_tick changes
  assert:
    that:
      - 'output_1 is changed'
      - 'output_1.actions[0] == "Swarm cluster updated"'
      - 'output_1.diff.before is defined'
      - 'output_1.diff.after is defined'
      - 'output_2 is changed'
      - 'output_2.actions[0] == "Swarm cluster updated"'
      - 'output_2.diff.before is defined'
      - 'output_2.diff.after is defined'
      - 'output_3 is not changed'
      - 'output_3.actions[0] == "No modification"'
      - 'output_3.diff.before is defined'
      - 'output_3.diff.after is defined'
      - 'output_4 is not changed'
      - 'output_4.actions[0] == "No modification"'
      - 'output_4.diff.before is defined'
      - 'output_4.diff.after is defined'
      - 'output_5 is changed'
      - 'output_5.actions[0] == "Swarm cluster updated"'
      - 'output_5.diff.before is defined'
      - 'output_5.diff.after is defined'
      - 'output_6 is changed'
      - 'output_6.actions[0] == "Swarm cluster updated"'
      - 'output_6.diff.before is defined'
      - 'output_6.diff.after is defined'

####################################################################
## heartbeat_tick ##################################################
####################################################################

- name: heartbeat_tick (check mode)
  docker_swarm:
    state: present
    heartbeat_tick: 2
  check_mode: true
  diff: true
  register: output_1

- name: heartbeat_tick
  docker_swarm:
    state: present
    heartbeat_tick: 2
  diff: true
  register: output_2

- name: heartbeat_tick (idempotent)
  docker_swarm:
    state: present
    heartbeat_tick: 2
  diff: true
  register: output_3

- name: heartbeat_tick (idempotent, check mode)
  docker_swarm:
    state: present
    heartbeat_tick: 2
  check_mode: true
  diff: true
  register: output_4

- name: heartbeat_tick (change, check mode)
  docker_swarm:
    state: present
    heartbeat_tick: 3
  check_mode: true
  diff: true
  register: output_5

- name: heartbeat_tick (change)
  docker_swarm:
    state: present
    heartbeat_tick: 3
  diff: true
  register: output_6

- name: assert heartbeat_tick changes
  assert:
    that:
      - 'output_1 is changed'
      - 'output_1.actions[0] == "Swarm cluster updated"'
      - 'output_1.diff.before is defined'
      - 'output_1.diff.after is defined'
      - 'output_2 is changed'
      - 'output_2.actions[0] == "Swarm cluster updated"'
      - 'output_2.diff.before is defined'
      - 'output_2.diff.after is defined'
      - 'output_3 is not changed'
      - 'output_3.actions[0] == "No modification"'
      - 'output_3.diff.before is defined'
      - 'output_3.diff.after is defined'
      - 'output_4 is not changed'
      - 'output_4.actions[0] == "No modification"'
      - 'output_4.diff.before is defined'
      - 'output_4.diff.after is defined'
      - 'output_5 is changed'
      - 'output_5.actions[0] == "Swarm cluster updated"'
      - 'output_5.diff.before is defined'
      - 'output_5.diff.after is defined'
      - 'output_6 is changed'
      - 'output_6.actions[0] == "Swarm cluster updated"'
      - 'output_6.diff.before is defined'
      - 'output_6.diff.after is defined'

####################################################################
## keep_old_snapshots ##############################################
####################################################################
- name: keep_old_snapshots (check mode)
  docker_swarm:
    state: present
    keep_old_snapshots: 1
  check_mode: true
  diff: true
  register: output_1

- name: keep_old_snapshots
  docker_swarm:
    state: present
    keep_old_snapshots: 1
  diff: true
  register: output_2

- name: keep_old_snapshots (idempotent)
  docker_swarm:
    state: present
    keep_old_snapshots: 1
  diff: true
  register: output_3

- name: keep_old_snapshots (idempotent, check mode)
  docker_swarm:
    state: present
    keep_old_snapshots: 1
  check_mode: true
  diff: true
  register: output_4

- name: keep_old_snapshots (change, check mode)
  docker_swarm:
    state: present
    keep_old_snapshots: 2
  check_mode: true
  diff: true
  register: output_5

- name: keep_old_snapshots (change)
  docker_swarm:
    state: present
    keep_old_snapshots: 2
  diff: true
  register: output_6

- name: assert keep_old_snapshots changes
  assert:
    that:
      - 'output_1 is changed'
      - 'output_1.actions[0] == "Swarm cluster updated"'
      - 'output_1.diff.before is defined'
      - 'output_1.diff.after is defined'
      - 'output_2 is changed'
      - 'output_2.actions[0] == "Swarm cluster updated"'
      - 'output_2.diff.before is defined'
      - 'output_2.diff.after is defined'
      - 'output_3 is not changed'
      - 'output_3.actions[0] == "No modification"'
      - 'output_3.diff.before is defined'
      - 'output_3.diff.after is defined'
      - 'output_4 is not changed'
      - 'output_4.actions[0] == "No modification"'
      - 'output_4.diff.before is defined'
      - 'output_4.diff.after is defined'
      - 'output_5 is changed'
      - 'output_5.actions[0] == "Swarm cluster updated"'
      - 'output_5.diff.before is defined'
      - 'output_5.diff.after is defined'
      - 'output_6 is changed'
      - 'output_6.actions[0] == "Swarm cluster updated"'
      - 'output_6.diff.before is defined'
      - 'output_6.diff.after is defined'

####################################################################
## labels ##########################################################
####################################################################
- name: labels (check mode)
  docker_swarm:
    state: present
    labels:
      a: v1
      b: v2
  check_mode: true
  diff: true
  register: output_1
  ignore_errors: true

- name: labels
  docker_swarm:
    state: present
    labels:
      a: v1
      b: v2
  diff: true
  register: output_2
  ignore_errors: true

- name: labels (idempotent)
  docker_swarm:
    state: present
    labels:
      a: v1
      b: v2
  diff: true
  register: output_3
  ignore_errors: true

- name: labels (idempotent, check mode)
  docker_swarm:
    state: present
    labels:
      a: v1
      b: v2
  check_mode: true
  diff: true
  register: output_4
  ignore_errors: true

- name: labels (change, check mode)
  docker_swarm:
    state: present
    labels:
      a: v1
      c: v3
  check_mode: true
  diff: true
  register: output_5
  ignore_errors: true

- name: labels (change)
  docker_swarm:
    state: present
    labels:
      a: v1
      c: v3
  diff: true
  register: output_6
  ignore_errors: true

- name: labels (not specifying, check mode)
  docker_swarm:
    state: present
  check_mode: true
  diff: true
  register: output_7
  ignore_errors: true

- name: labels (not specifying)
  docker_swarm:
    state: present
  diff: true
  register: output_8
  ignore_errors: true

- name: labels (idempotency, check that labels are still there)
  docker_swarm:
    state: present
    labels:
      a: v1
      c: v3
  diff: true
  register: output_9
  ignore_errors: true

- name: labels (empty, check mode)
  docker_swarm:
    state: present
    labels: {}
  check_mode: true
  diff: true
  register: output_10
  ignore_errors: true

- name: labels (empty)
  docker_swarm:
    state: present
    labels: {}
  diff: true
  register: output_11
  ignore_errors: true

- name: labels (empty, idempotent, check mode)
  docker_swarm:
    state: present
    labels: {}
  check_mode: true
  diff: true
  register: output_12
  ignore_errors: true

- name: labels (empty, idempotent)
  docker_swarm:
    state: present
    labels: {}
  diff: true
  register: output_13
  ignore_errors: true

- name: assert labels changes
  assert:
    that:
      - 'output_1 is changed'
      - 'output_1.actions[0] == "Swarm cluster updated"'
      - 'output_1.diff.before is defined'
      - 'output_1.diff.after is defined'
      - 'output_2 is changed'
      - 'output_2.actions[0] == "Swarm cluster updated"'
      - 'output_2.diff.before is defined'
      - 'output_2.diff.after is defined'
      - 'output_3 is not changed'
      - 'output_3.actions[0] == "No modification"'
      - 'output_3.diff.before is defined'
      - 'output_3.diff.after is defined'
      - 'output_4 is not changed'
      - 'output_4.actions[0] == "No modification"'
      - 'output_4.diff.before is defined'
      - 'output_4.diff.after is defined'
      - 'output_5 is changed'
      - 'output_5.actions[0] == "Swarm cluster updated"'
      - 'output_5.diff.before is defined'
      - 'output_5.diff.after is defined'
      - 'output_6 is changed'
      - 'output_6.actions[0] == "Swarm cluster updated"'
      - 'output_6.diff.before is defined'
      - 'output_6.diff.after is defined'
      - 'output_7 is not changed'
      - 'output_7.actions[0] == "No modification"'
      - 'output_7.diff.before is defined'
      - 'output_7.diff.after is defined'
      - 'output_8 is not changed'
      - 'output_8.actions[0] == "No modification"'
      - 'output_8.diff.before is defined'
      - 'output_8.diff.after is defined'
      - 'output_9 is not changed'
      - 'output_9.actions[0] == "No modification"'
      - 'output_9.diff.before is defined'
      - 'output_9.diff.after is defined'
      - 'output_10 is changed'
      - 'output_10.actions[0] == "Swarm cluster updated"'
      - 'output_10.diff.before is defined'
      - 'output_10.diff.after is defined'
      - 'output_11 is changed'
      - 'output_11.actions[0] == "Swarm cluster updated"'
      - 'output_11.diff.before is defined'
      - 'output_11.diff.after is defined'
      - 'output_12 is not changed'
      - 'output_12.actions[0] == "No modification"'
      - 'output_12.diff.before is defined'
      - 'output_12.diff.after is defined'
      - 'output_13 is not changed'
      - 'output_13.actions[0] == "No modification"'
      - 'output_13.diff.before is defined'
      - 'output_13.diff.after is defined'
  when: docker_py_version is version('2.6.0', '>=')
- assert:
    that:
      - output_1 is failed
      - "('version is ' ~ docker_py_version ~ ' ') in output_1.msg"
      - "'Minimum version required is 2.6.0 ' in output_1.msg"
  when: docker_py_version is version('2.6.0', '<')

####################################################################
## log_entries_for_slow_followers ##################################
####################################################################
- name: log_entries_for_slow_followers (check mode)
  docker_swarm:
    state: present
    log_entries_for_slow_followers: 42
  check_mode: true
  diff: true
  register: output_1

- name: log_entries_for_slow_followers
  docker_swarm:
    state: present
    log_entries_for_slow_followers: 42
  diff: true
  register: output_2

- name: log_entries_for_slow_followers (idempotent)
  docker_swarm:
    state: present
    log_entries_for_slow_followers: 42
  diff: true
  register: output_3

- name: log_entries_for_slow_followers (idempotent, check mode)
  docker_swarm:
    state: present
    log_entries_for_slow_followers: 42
  check_mode: true
  diff: true
  register: output_4

- name: log_entries_for_slow_followers (change, check mode)
  docker_swarm:
    state: present
    log_entries_for_slow_followers: 23
  check_mode: true
  diff: true
  register: output_5

- name: log_entries_for_slow_followers (change)
  docker_swarm:
    state: present
    log_entries_for_slow_followers: 23
  diff: true
  register: output_6

- name: assert log_entries_for_slow_followers changes
  assert:
    that:
      - 'output_1 is changed'
      - 'output_1.actions[0] == "Swarm cluster updated"'
      - 'output_1.diff.before is defined'
      - 'output_1.diff.after is defined'
      - 'output_2 is changed'
      - 'output_2.actions[0] == "Swarm cluster updated"'
      - 'output_2.diff.before is defined'
      - 'output_2.diff.after is defined'
      - 'output_3 is not changed'
      - 'output_3.actions[0] == "No modification"'
      - 'output_3.diff.before is defined'
      - 'output_3.diff.after is defined'
      - 'output_4 is not changed'
      - 'output_4.actions[0] == "No modification"'
      - 'output_4.diff.before is defined'
      - 'output_4.diff.after is defined'
      - 'output_5 is changed'
      - 'output_5.actions[0] == "Swarm cluster updated"'
      - 'output_5.diff.before is defined'
      - 'output_5.diff.after is defined'
      - 'output_6 is changed'
      - 'output_6.actions[0] == "Swarm cluster updated"'
      - 'output_6.diff.before is defined'
      - 'output_6.diff.after is defined'

####################################################################
## name ############################################################
####################################################################
- name: name (idempotent, check mode)
  docker_swarm:
    state: present
    name: default
  check_mode: true
  diff: true
  register: output_1

- name: name (idempotent)
  docker_swarm:
    state: present
    name: default
  diff: true
  register: output_2

# The name 'default' is hardcoded in docker swarm. Trying to change
# it causes a failure. This might change in the future, so we also
# accept a change for this test.
- name: name (change, should fail)
  docker_swarm:
    state: present
    name: foobar
  diff: true
  register: output_3
  ignore_errors: true

- name: assert name changes
  assert:
    that:
      - 'output_1 is not changed'
      - 'output_1.actions[0] == "No modification"'
      - 'output_1.diff.before is defined'
      - 'output_1.diff.after is defined'
      - 'output_2 is not changed'
      - 'output_2.actions[0] == "No modification"'
      - 'output_2.diff.before is defined'
      - 'output_2.diff.after is defined'
      - 'output_3 is failed or output_3 is changed'

####################################################################
## node_cert_expiry ################################################
####################################################################
- name: node_cert_expiry (check mode)
  docker_swarm:
    state: present
    node_cert_expiry: 7896000000000000
  check_mode: true
  diff: true
  register: output_1

- name: node_cert_expiry
  docker_swarm:
    state: present
    node_cert_expiry: 7896000000000000
  diff: true
  register: output_2

- name: node_cert_expiry (idempotent)
  docker_swarm:
    state: present
    node_cert_expiry: 7896000000000000
  diff: true
  register: output_3

- name: node_cert_expiry (idempotent, check mode)
  docker_swarm:
    state: present
    node_cert_expiry: 7896000000000000
  check_mode: true
  diff: true
  register: output_4

- name: node_cert_expiry (change, check mode)
  docker_swarm:
    state: present
    node_cert_expiry: 8766000000000000
  check_mode: true
  diff: true
  register: output_5

- name: node_cert_expiry (change)
  docker_swarm:
    state: present
    node_cert_expiry: 8766000000000000
  diff: true
  register: output_6

- name: assert node_cert_expiry changes
  assert:
    that:
      - 'output_1 is changed'
      - 'output_1.actions[0] == "Swarm cluster updated"'
      - 'output_1.diff.before is defined'
      - 'output_1.diff.after is defined'
      - 'output_2 is changed'
      - 'output_2.actions[0] == "Swarm cluster updated"'
      - 'output_2.diff.before is defined'
      - 'output_2.diff.after is defined'
      - 'output_3 is not changed'
      - 'output_3.actions[0] == "No modification"'
      - 'output_3.diff.before is defined'
      - 'output_3.diff.after is defined'
      - 'output_4 is not changed'
      - 'output_4.actions[0] == "No modification"'
      - 'output_4.diff.before is defined'
      - 'output_4.diff.after is defined'
      - 'output_5 is changed'
      - 'output_5.actions[0] == "Swarm cluster updated"'
      - 'output_5.diff.before is defined'
      - 'output_5.diff.after is defined'
      - 'output_6 is changed'
      - 'output_6.actions[0] == "Swarm cluster updated"'
      - 'output_6.diff.before is defined'
      - 'output_6.diff.after is defined'

####################################################################
## rotate_manager_token ############################################
####################################################################
- name: rotate_manager_token (true, check mode)
  docker_swarm:
    state: present
    rotate_manager_token: true
  check_mode: true
  diff: true
  register: output_1

- name: rotate_manager_token (true)
  docker_swarm:
    state: present
    rotate_manager_token: true
  diff: true
  register: output_2

- name: rotate_manager_token (false, idempotent)
  docker_swarm:
    state: present
    rotate_manager_token: false
  diff: true
  register: output_3

- name: rotate_manager_token (false, check mode)
  docker_swarm:
    state: present
    rotate_manager_token: false
  check_mode: true
  diff: true
  register: output_4

- name: assert rotate_manager_token changes
  assert:
    that:
      - 'output_1 is changed'
      - 'output_1.actions[0] == "Swarm cluster updated"'
      - 'output_1.diff.before is defined'
      - 'output_1.diff.after is defined'
      - 'output_2 is changed'
      - 'output_2.actions[0] == "Swarm cluster updated"'
      - 'output_2.diff.before is defined'
      - 'output_2.diff.after is defined'
      - 'output_3 is not changed'
      - 'output_3.actions[0] == "No modification"'
      - 'output_3.diff.before is defined'
      - 'output_3.diff.after is defined'
      - 'output_4 is not changed'
      - 'output_4.actions[0] == "No modification"'
      - 'output_4.diff.before is defined'
      - 'output_4.diff.after is defined'

####################################################################
## rotate_worker_token #############################################
####################################################################
- name: rotate_worker_token (true, check mode)
  docker_swarm:
    state: present
    rotate_worker_token: true
  check_mode: true
  diff: true
  register: output_1

- name: rotate_worker_token (true)
  docker_swarm:
    state: present
    rotate_worker_token: true
  diff: true
  register: output_2

- name: rotate_worker_token (false, idempotent)
  docker_swarm:
    state: present
    rotate_worker_token: false
  diff: true
  register: output_3

- name: rotate_worker_token (false, check mode)
  docker_swarm:
    state: present
    rotate_worker_token: false
  check_mode: true
  diff: true
  register: output_4

- name: assert rotate_worker_token changes
  assert:
    that:
      - 'output_1 is changed'
      - 'output_1.actions[0] == "Swarm cluster updated"'
      - 'output_1.diff.before is defined'
      - 'output_1.diff.after is defined'
      - 'output_2 is changed'
      - 'output_2.actions[0] == "Swarm cluster updated"'
      - 'output_2.diff.before is defined'
      - 'output_2.diff.after is defined'
      - 'output_3 is not changed'
      - 'output_3.actions[0] == "No modification"'
      - 'output_3.diff.before is defined'
      - 'output_3.diff.after is defined'
      - 'output_4 is not changed'
      - 'output_4.actions[0] == "No modification"'
      - 'output_4.diff.before is defined'
      - 'output_4.diff.after is defined'

####################################################################
## snapshot_interval ###############################################
####################################################################
- name: snapshot_interval (check mode)
  docker_swarm:
    state: present
    snapshot_interval: 12345
  check_mode: true
  diff: true
  register: output_1

- name: snapshot_interval
  docker_swarm:
    state: present
    snapshot_interval: 12345
  diff: true
  register: output_2

- name: snapshot_interval (idempotent)
  docker_swarm:
    state: present
    snapshot_interval: 12345
  diff: true
  register: output_3

- name: snapshot_interval (idempotent, check mode)
  docker_swarm:
    state: present
    snapshot_interval: 12345
  check_mode: true
  diff: true
  register: output_4

- name: snapshot_interval (change, check mode)
  docker_swarm:
    state: present
    snapshot_interval: 54321
  check_mode: true
  diff: true
  register: output_5

- name: snapshot_interval (change)
  docker_swarm:
    state: present
    snapshot_interval: 54321
  diff: true
  register: output_6

- name: assert snapshot_interval changes
  assert:
    that:
      - 'output_1 is changed'
      - 'output_1.actions[0] == "Swarm cluster updated"'
      - 'output_1.diff.before is defined'
      - 'output_1.diff.after is defined'
      - 'output_2 is changed'
      - 'output_2.actions[0] == "Swarm cluster updated"'
      - 'output_2.diff.before is defined'
      - 'output_2.diff.after is defined'
      - 'output_3 is not changed'
      - 'output_3.actions[0] == "No modification"'
      - 'output_3.diff.before is defined'
      - 'output_3.diff.after is defined'
      - 'output_4 is not changed'
      - 'output_4.actions[0] == "No modification"'
      - 'output_4.diff.before is defined'
      - 'output_4.diff.after is defined'
      - 'output_5 is changed'
      - 'output_5.actions[0] == "Swarm cluster updated"'
      - 'output_5.diff.before is defined'
      - 'output_5.diff.after is defined'
      - 'output_6 is changed'
      - 'output_6.actions[0] == "Swarm cluster updated"'
      - 'output_6.diff.before is defined'
      - 'output_6.diff.after is defined'

####################################################################
## task_history_retention_limit ####################################
####################################################################
- name: task_history_retention_limit (check mode)
  docker_swarm:
    state: present
    task_history_retention_limit: 23
  check_mode: true
  diff: true
  register: output_1

- name: task_history_retention_limit
  docker_swarm:
    state: present
    task_history_retention_limit: 23
  diff: true
  register: output_2

- name: task_history_retention_limit (idempotent)
  docker_swarm:
    state: present
    task_history_retention_limit: 23
  diff: true
  register: output_3

- name: task_history_retention_limit (idempotent, check mode)
  docker_swarm:
    state: present
    task_history_retention_limit: 23
  check_mode: true
  diff: true
  register: output_4

- name: task_history_retention_limit (change, check mode)
  docker_swarm:
    state: present
    task_history_retention_limit: 7
  check_mode: true
  diff: true
  register: output_5

- name: task_history_retention_limit (change)
  docker_swarm:
    state: present
    task_history_retention_limit: 7
  diff: true
  register: output_6

- name: assert task_history_retention_limit changes
  assert:
    that:
      - 'output_1 is changed'
      - 'output_1.actions[0] == "Swarm cluster updated"'
      - 'output_1.diff.before is defined'
      - 'output_1.diff.after is defined'
      - 'output_2 is changed'
      - 'output_2.actions[0] == "Swarm cluster updated"'
      - 'output_2.diff.before is defined'
      - 'output_2.diff.after is defined'
      - 'output_3 is not changed'
      - 'output_3.actions[0] == "No modification"'
      - 'output_3.diff.before is defined'
      - 'output_3.diff.after is defined'
      - 'output_4 is not changed'
      - 'output_4.actions[0] == "No modification"'
      - 'output_4.diff.before is defined'
      - 'output_4.diff.after is defined'
      - 'output_5 is changed'
      - 'output_5.actions[0] == "Swarm cluster updated"'
      - 'output_5.diff.before is defined'
      - 'output_5.diff.after is defined'
      - 'output_6 is changed'
      - 'output_6.actions[0] == "Swarm cluster updated"'
      - 'output_6.diff.before is defined'
      - 'output_6.diff.after is defined'

- include_tasks: cleanup.yml
