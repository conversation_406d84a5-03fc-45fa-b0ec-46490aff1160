---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- hosts: 127.0.0.1
  connection: local
  gather_facts: true
  tasks:
    - name: remove docker containers
      docker_container:
        name: "{{ item }}"
        state: absent
        force_kill: true
      loop:
        - ansible-docker-test-docker-inventory-container-1
        - ansible-docker-test-docker-inventory-container-2
