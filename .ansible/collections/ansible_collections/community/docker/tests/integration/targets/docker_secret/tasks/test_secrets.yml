---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- block:
    - name: Make sure we're not already using Docker swarm
      docker_swarm:
        state: absent
        force: true

    - name: Create a Swarm cluster
      docker_swarm:
        state: present
        advertise_addr: "{{ ansible_default_ipv4.address | default('127.0.0.1') }}"

    - name: Parameter name should be required
      docker_secret:
        state: present
      ignore_errors: true
      register: output

    - name: assert failure when called with no name
      assert:
        that:
          - 'output.failed'
          - 'output.msg == "missing required arguments: name"'

    - name: Test parameters
      docker_secret:
        name: foo
        state: present
      ignore_errors: true
      register: output

    - name: assert failure when called with no data
      assert:
        that:
          - 'output.failed'
          - 'output.msg == "state is present but any of the following are missing: data, data_src"'

    - name: Create secret
      docker_secret:
        name: db_password
        data: opensesame!
        state: present
      register: output

    - name: Create variable secret_id
      set_fact:
        secret_id: "{{ output.secret_id }}"

    - name: Inspect secret
      command: "docker secret inspect {{ secret_id }}"
      register: inspect
      ignore_errors: true

    - debug: var=inspect

    - name: assert secret creation succeeded
      assert:
        that:
          - "'db_password' in inspect.stdout"
          - "'ansible_key' in inspect.stdout"
      when: inspect is not failed
    - assert:
        that:
          - "'is too new. Maximum supported API version is' in inspect.stderr"
      when: inspect is failed

    - name: Create secret again
      docker_secret:
        name: db_password
        data: opensesame!
        state: present
      register: output

    - name: assert create secret is idempotent
      assert:
        that:
          - not output.changed

    - name: Write secret into file
      copy:
        dest: "{{ remote_tmp_dir }}/data"
        content: |-
          opensesame!

    - name: Create secret again (from file)
      docker_secret:
        name: db_password
        data_src: "{{ remote_tmp_dir }}/data"
        state: present
      register: output

    - name: assert create secret is idempotent
      assert:
        that:
          - not output.changed

    - name: Create secret again (base64)
      docker_secret:
        name: db_password
        data: b3BlbnNlc2FtZSE=
        data_is_b64: true
        state: present
      register: output

    - name: assert create secret (base64) is idempotent
      assert:
        that:
          - not output.changed

    - name: Update secret
      docker_secret:
        name: db_password
        data: newpassword!
        state: present
      register: output

    - name: assert secret was updated
      assert:
        that:
          - output.changed
          - output.secret_id != secret_id

    - name: Remove secret
      docker_secret:
        name: db_password
        state: absent

    - name: Check that secret is removed
      command: "docker secret inspect {{ secret_id }}"
      register: output
      ignore_errors: true

    - name: assert secret was removed
      assert:
        that:
          - output.failed

  # Rolling update

    - name: Create rolling secret
      docker_secret:
        name: rolling_password
        data: opensesame!
        rolling_versions: true
        state: present
      register: original_output

    - name: Create variable secret_id
      set_fact:
        secret_id: "{{ original_output.secret_id }}"

    - name: Inspect secret
      command: "docker secret inspect {{ secret_id }}"
      register: inspect
      ignore_errors: true

    - debug: var=inspect

    - name: assert secret creation succeeded
      assert:
        that:
          - "'rolling_password' in inspect.stdout"
          - "'ansible_key' in inspect.stdout"
          - "'ansible_version' in inspect.stdout"
          - original_output.secret_name == 'rolling_password_v1'
      when: inspect is not failed
    - assert:
        that:
          - "'is too new. Maximum supported API version is' in inspect.stderr"
      when: inspect is failed

    - name: Create secret again
      docker_secret:
        name: rolling_password
        data: newpassword!
        rolling_versions: true
        state: present
      register: new_output

    - name: assert that new version is created
      assert:
        that:
          - new_output.changed
          - new_output.secret_id != original_output.secret_id
          - new_output.secret_name != original_output.secret_name
          - new_output.secret_name == 'rolling_password_v2'

    - name: Remove rolling secrets
      docker_secret:
        name: rolling_password
        rolling_versions: true
        state: absent

    - name: Check that secret is removed
      command: "docker secret inspect {{ original_output.secret_id }}"
      register: output
      ignore_errors: true

    - name: assert secret was removed
      assert:
        that:
          - output.failed

    - name: Check that secret is removed
      command: "docker secret inspect {{ new_output.secret_id }}"
      register: output
      ignore_errors: true

    - name: assert secret was removed
      assert:
        that:
          - output.failed

  always:
    - name: Remove Swarm cluster
      docker_swarm:
        state: absent
        force: true
