---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Register registry cleanup
  # This must be done **before** docker is set up (see next task), to ensure that the
  # registry is removed **before** docker itself is removed. This is necessary as the
  # registry and its frontends run as docker containers.
  command: 'true'
  notify: Remove test registry

- name: Setup Docker
  # Please note that we do setup_docker here and not via meta/main.yml to avoid the problem that
  # our cleanup is called **after** setup_docker's cleanup has been called!
  include_role:
    name: setup_docker

- name: Figure out current container's network IP
  include_role:
    name: setup_docker_current_container_network_ip

- name: Create random name prefix and test registry name
  set_fact:
    docker_registry_container_name_registry: '{{ ''ansible-docker-test-registry-%0x'' % ((2**32) | random) }}'
    docker_registry_container_name_nginx: '{{ ''ansible-docker-test-registry-frontend-%0x'' % ((2**32) | random) }}'
    docker_registry_container_name_nginx2: '{{ ''ansible-docker-test-registry-frontend2-%0x'' % ((2**32) | random) }}'

- name: Create image and container list
  set_fact:
    docker_registry_setup_inames: []
    docker_registry_setup_cnames:
      - '{{ docker_registry_container_name_registry }}'
      - '{{ docker_registry_container_name_nginx }}'
      - '{{ docker_registry_container_name_nginx2 }}'
    docker_registry_setup_vnames:
      - '{{ docker_registry_container_name_nginx }}'
      - '{{ docker_registry_container_name_nginx2 }}'

- debug:
    msg: Using test registry name {{ docker_registry_container_name_registry }} and nginx frontend names {{ docker_registry_container_name_nginx }} and {{ docker_registry_container_name_nginx2 }}

- fail: msg="Too old docker version to set up docker registry!"
  when: not(docker_api_version is version('1.25', '>=')) and (ansible_distribution != 'CentOS' or ansible_distribution_major_version|int > 6)

- when: docker_api_version is version('1.25', '>=')
  block:

  # Set up registry container
    - name: Start test registry
      docker_container:
        name: '{{ docker_registry_container_name_registry }}'
        image: "{{ docker_test_image_registry }}"
        ports: 5000
        network_mode: '{{ current_container_network_ip | default(omit, true) }}'
        # We need to define the alias `real-registry` here because the global `links`
        # option for the NGINX containers (see setup-frontend.yml) does not work when
        # using networks.
        networks: >-
          {{
            [dict([['name', current_container_network_ip], ['aliases', ['real-registry']]])]
            if current_container_network_ip not in ['', 'bridge'] else omit
          }}
      register: registry_container

    - name: Get registry URL
      set_fact:
        registry_address: localhost:{{ registry_container.container.NetworkSettings.Ports['5000/tcp'].0.HostPort }}

    # Set up first nginx frontend for registry
    - include_tasks: setup-frontend.yml
      vars:
        docker_registry_container_name_frontend: '{{ docker_registry_container_name_nginx }}'

    - set_fact:
        registry_frontend_address: '{{ docker_registry_frontend_address }}'

    # Set up second nginx frontend for registry
    - include_tasks: setup-frontend.yml
      vars:
        docker_registry_container_name_frontend: '{{ docker_registry_container_name_nginx2 }}'

    - set_fact:
        registry_frontend2_address: '{{ docker_registry_frontend_address }}'

    # Print addresses for registry and frontends
    - debug:
        msg: "Registry available under {{ registry_address }}, NGINX frontends available under {{ registry_frontend_address }} and {{ registry_frontend2_address }}"
