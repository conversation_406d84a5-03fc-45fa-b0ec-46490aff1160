---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: "Make sure all images are removed"
  docker_image_remove:
    name: "{{ item }}"
  with_items: "{{ docker_registry_setup_inames }}"

- name: "Get registry logs"
  command: "docker logs {{ docker_registry_container_name_registry }}"
  register: registry_logs
  no_log: true
  ignore_errors: true

- name: "Printing registry logs"
  debug:
    var: registry_logs.stdout_lines
  when: registry_logs is not failed

- name: "Get nginx logs for first instance"
  command: "docker logs {{ docker_registry_container_name_nginx }}"
  register: nginx_logs
  no_log: true
  ignore_errors: true

- name: "Get nginx logs for second instance"
  command: "docker logs {{ docker_registry_container_name_nginx2 }}"
  register: nginx2_logs
  no_log: true
  ignore_errors: true

- name: "Printing nginx logs for first instance"
  debug:
    var: nginx_logs.stdout_lines
  when: nginx_logs is not failed

- name: "Printing nginx logs for second instance"
  debug:
    var: nginx2_logs.stdout_lines
  when: nginx_logs is not failed

- name: "Make sure all containers are removed"
  docker_container:
    name: "{{ item }}"
    state: absent
    force_kill: true
  with_items: "{{ docker_registry_setup_cnames }}"
  register: result
  retries: 3
  delay: 3
  until: result is success

- name: "Make sure all volumes are removed"
  command: "docker volume rm -f {{ item }}"
  with_items: "{{ docker_registry_setup_vnames }}"
  ignore_errors: true
