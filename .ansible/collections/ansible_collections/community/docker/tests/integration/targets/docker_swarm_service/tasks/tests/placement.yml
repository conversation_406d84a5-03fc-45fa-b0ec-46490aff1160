---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Registering service name
  set_fact:
    service_name: "{{ name_prefix ~ '-placement' }}"

- name: Registering service name
  set_fact:
    service_names: "{{ service_names + [service_name] }}"


####################################################################
## placement.preferences ###########################################
####################################################################

- name: placement.preferences
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    placement:
      preferences:
        - spread: "node.labels.test"
  register: placement_preferences_1
  ignore_errors: true

- name: placement.preferences (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    placement:
      preferences:
        - spread: "node.labels.test"
  register: placement_preferences_2
  ignore_errors: true

- name: placement.preferences (change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    placement:
      preferences:
        - spread: "node.labels.test2"
  register: placement_preferences_3
  ignore_errors: true

- name: placement.preferences (empty)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    placement:
      preferences: []
  register: placement_preferences_4
  ignore_errors: true

- name: placement.preferences (empty idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    placement:
      preferences: []
  register: placement_preferences_5
  ignore_errors: true

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - placement_preferences_1 is changed
      - placement_preferences_2 is not changed
      - placement_preferences_3 is changed
      - placement_preferences_4 is changed
      - placement_preferences_5 is not changed
  when: docker_api_version is version('1.27', '>=') and docker_py_version is version('2.4.0', '>=')
- assert:
    that:
      - placement_preferences_1 is failed
      - "'Minimum version required' in placement_preferences_1.msg"
  when: docker_api_version is version('1.27', '<') or docker_py_version is version('2.4.0', '<')

####################################################################
## placement.constraints #####################################################
####################################################################

- name: placement.constraints
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    placement:
      constraints:
        - "node.role == manager"
  register: constraints_1
  ignore_errors: true

- name: placement.constraints (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    placement:
      constraints:
        - "node.role == manager"
  register: constraints_2
  ignore_errors: true

- name: placement.constraints (change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    placement:
      constraints:
        - "node.role == worker"
  register: constraints_3
  ignore_errors: true

- name: placement.constraints (add)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    placement:
      constraints:
        - "node.role == worker"
        - "node.label != non_existent_label"
  register: constraints_4
  ignore_errors: true

- name: placement.constraints (order idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    placement:
      constraints:
        - "node.label != non_existent_label"
        - "node.role == worker"
  register: constraints_5
  ignore_errors: true

- name: placement.constraints (empty)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    placement:
      constraints: []
  register: constraints_6
  ignore_errors: true

- name: placement.constraints (empty idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    placement:
      constraints: []
  register: constraints_7
  ignore_errors: true

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - constraints_1 is changed
      - constraints_2 is not changed
      - constraints_3 is changed
      - constraints_4 is changed
      - constraints_5 is not changed
      - constraints_6 is changed
      - constraints_7 is not changed
  when: docker_api_version is version('1.27', '>=') and docker_py_version is version('2.4.0', '>=')
- assert:
    that:
      - constraints_1 is failed
      - "'Minimum version required' in constraints_1.msg"
  when: docker_api_version is version('1.27', '<') or docker_py_version is version('2.4.0', '<')

####################################################################
## placement.replicas_max_per_node #####################################################
####################################################################

- name: placement.replicas_max_per_node
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    placement:
      replicas_max_per_node: 1
  register: replicas_max_per_node_1
  ignore_errors: true

- name: placement.replicas_max_per_node (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    placement:
      replicas_max_per_node: 1
  register: replicas_max_per_node_2
  ignore_errors: true

- name: placement.replicas_max_per_node (change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    placement:
      replicas_max_per_node: 2
  register: replicas_max_per_node_3
  ignore_errors: true


- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - replicas_max_per_node_1 is changed
      - replicas_max_per_node_2 is not changed
      - replicas_max_per_node_3 is changed
  when: docker_api_version is version('1.40', '>=') and docker_py_version is version('4.4.3', '>=')
- assert:
    that:
      - replicas_max_per_node_1 is failed
      - "'Minimum version required' in replicas_max_per_node_1.msg"
  when: docker_api_version is version('1.40', '<') or docker_py_version is version('4.4.3', '<')
