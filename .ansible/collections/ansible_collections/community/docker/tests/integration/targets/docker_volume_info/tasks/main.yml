---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

- block:
    - name: Create random volume name
      set_fact:
        cname: "{{ 'ansible-docker-test-%0x' % ((2**32) | random) }}"

    - name: Make sure volume is not there
      docker_volume:
        name: "{{ cname }}"
        state: absent

    - name: Inspect a non-present volume
      docker_volume_info:
        name: "{{ cname }}"
      register: result

    - assert:
        that:
          - "not result.exists"
          - "'volume' in result"
          - "result.volume is none"

    - name: Make sure volume exists
      docker_volume:
        name: "{{ cname }}"

    - name: Inspect a present volume
      docker_volume_info:
        name: "{{ cname }}"
      register: result
    - name: Dump docker_volume_info result
      debug: var=result

    - name: "Comparison: use 'docker volume inspect'"
      command: docker volume inspect "{{ cname }}"
      register: docker_volume_inspect
      ignore_errors: true
    - block:
        - set_fact:
            docker_volume_inspect_result: "{{ docker_volume_inspect.stdout | from_json }}"
        - name: Dump docker volume inspect result
          debug: var=docker_volume_inspect_result
      when: docker_volume_inspect is not failed

    - name: Cleanup
      docker_volume:
        name: "{{ cname }}"
        state: absent

    - assert:
        that:
          - result.exists
          - "'volume' in result"
          - "result.volume is truthy"

    - assert:
        that:
          - "result.volume == docker_volume_inspect_result[0]"
      when: docker_volume_inspect is not failed
    - assert:
        that:
          - "'is too new. Maximum supported API version is' in docker_volume_inspect.stderr"
      when: docker_volume_inspect is failed

  when: docker_api_version is version('1.25', '>=')

- fail: msg="Too old docker / docker-py version to run docker_volume_info tests!"
  when: not(docker_api_version is version('1.25', '>=')) and (ansible_distribution != 'CentOS' or ansible_distribution_major_version|int > 6)
