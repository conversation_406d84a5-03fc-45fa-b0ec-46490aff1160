---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Include distribution specific variables
  include_vars: "{{ lookup('first_found', params) }}"
  vars:
    params:
      files:
        - "{{ ansible_facts.distribution }}-{{ ansible_facts.distribution_major_version }}.yml"
        - "{{ ansible_facts.os_family }}-{{ ansible_facts.distribution_major_version }}.yml"
        - "{{ ansible_facts.distribution }}.yml"
        - "{{ ansible_facts.os_family }}.yml"
        - default.yml
      paths:
        - "{{ role_path }}/vars"

- block:
    - name: Include distribution specific tasks
      include_tasks: "{{ lookup('first_found', params) }}"
      vars:
        params:
          files:
            - "{{ ansible_facts.distribution }}-{{ ansible_facts.distribution_major_version }}.yml"
            - "{{ ansible_facts.os_family }}-{{ ansible_facts.distribution_major_version }}.yml"
            - "{{ ansible_facts.distribution }}.yml"
            - "{{ ansible_facts.os_family }}.yml"
          paths:
            - "{{ role_path }}/tasks"

    - name: Install Python on Whales
      pip:
        state: present
        name: python-on-whales
        extra_args: "-c {{ remote_constraints }}"

    - name: Register docker-compose version
      command: "docker compose version --short"
      register: docker_compose_version

    - name: Declare docker-compose version
      set_fact:
        docker_compose_version: "{{ docker_compose_version.stdout }}"

    - name: Declare docker-compose as existing
      set_fact:
        has_docker_compose: '{{ docker_compose_version is version("2.0", ">=") }}'

  when: not skip_docker_compose
