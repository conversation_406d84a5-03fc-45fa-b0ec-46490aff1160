---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

service_expected_output:
  args: [sleep, '1800']
  cap_add: null
  cap_drop: null
  configs: null
  constraints: null
  container_labels: null
  command: null
  dns: null
  dns_options: null
  dns_search: null
  endpoint_mode: vip
  env: null
  force_update: null
  groups: null
  healthcheck: null
  healthcheck_disabled: null
  hostname: null
  hosts: null
  image: "{{ docker_test_image_busybox }}"
  labels: null
  limit_cpu: null
  limit_memory: null
  log_driver: null
  log_driver_options: null
  mode: global
  mounts: null
  networks: null
  secrets: null
  stop_grace_period: null
  stop_signal: null
  placement_preferences: null
  publish:
    - {mode: null, protocol: tcp, published_port: 60001, target_port: 60001}
    - {mode: null, protocol: udp, published_port: 60001, target_port: 60001}
  read_only: null
  replicas: null
  replicas_max_per_node: null
  reserve_cpu: null
  reserve_memory: null
  restart_policy: null
  restart_policy_attempts: null
  restart_policy_delay: null
  restart_policy_window: null
  rollback_config: null
  tty: null
  update_delay: null
  update_failure_action: null
  update_max_failure_ratio: null
  update_monitor: null
  update_order: null
  update_parallelism: null
  user: null
  working_dir: null
  init: null
  sysctls: null
