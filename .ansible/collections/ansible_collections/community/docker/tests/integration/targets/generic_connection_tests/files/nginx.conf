# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

user root;

events {
    worker_connections 16;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    error_log /dev/stdout info;
    access_log /dev/stdout;

    server {
        listen *:5000 ssl;
        server_name daemon-tls.ansible.com;
        server_name_in_redirect on;

        ssl_protocols TLSv1.2;
        ssl_ciphers 'ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384:DHE-DSS-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:DHE-DSS-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-SHA384:ECDHE-RSA-AES256-SHA384:ECDHE-ECDSA-AES128-SHA256:ECDHE-RSA-AES128-SHA256:DHE-RSA-AES256-SHA256:DHE-RSA-AES128-SHA256';
        ssl_ecdh_curve X25519:secp521r1:secp384r1;
        ssl_prefer_server_ciphers on;
        ssl_certificate /etc/nginx/cert.pem;
        ssl_certificate_key /etc/nginx/cert.key;

        location / {
            proxy_pass http://unix:/var/run/docker.sock:/;

            client_max_body_size 0;
            chunked_transfer_encoding on;
        }
    }

    server {
        listen *:6000;
        server_name daemon.ansible.com;
        server_name_in_redirect on;

        location / {
            proxy_pass http://unix:/var/run/docker.sock:/;

            client_max_body_size 0;
            chunked_transfer_encoding on;
        }
    }
}
