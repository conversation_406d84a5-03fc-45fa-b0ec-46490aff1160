---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- hosts: 127.0.0.1
  connection: local  # otherwise Ansible will complain that it cannot connect via ssh to 127.0.0.1:22
  gather_facts: false
  tasks:
    - name: Show all groups
      debug:
        var: groups
    - name: Make sure that the default groups are there, but no others
      assert:
        that:
          - groups.all | length >= 2
          - groups.ungrouped | length >= 2
          - groups | length == 2

- hosts: all
  gather_facts: false
  tasks:
    - when:
        # When the integration tests are run inside a docker container, there
        # will be other containers.
        - inventory_hostname.startswith('ansible-docker-test-docker-inventory-container-')
      block:
        - name: Run raw command
          raw: ls /
          register: output

        - name: Check whether we have some directories we expect in the output
          assert:
            that:
              - "'bin' in output.stdout_lines"
              - "'dev' in output.stdout_lines"
              - "'lib' in output.stdout_lines"
              - "'proc' in output.stdout_lines"
              - "'sys' in output.stdout_lines"
