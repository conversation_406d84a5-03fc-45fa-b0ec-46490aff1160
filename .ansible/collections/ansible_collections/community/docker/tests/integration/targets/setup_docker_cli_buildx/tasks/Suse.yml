---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Buildx is available in OpenSuSE 15.5, not sure which versions before
  set_fact:
    docker_has_buildx: "{{ ansible_facts.distribution_version is version('15.5', '>=') }}"

- name: Install Docker buildx CLI plugin
  community.general.zypper:
    name: docker-buildx
    disable_gpg_check: true
  when: docker_has_buildx
