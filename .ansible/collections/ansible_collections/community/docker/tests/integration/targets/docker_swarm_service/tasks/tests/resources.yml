---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Registering service name
  set_fact:
    service_name: "{{ name_prefix ~ '-resources' }}"

- name: Registering service name
  set_fact:
    service_names: "{{ service_names + [service_name] }}"

####################################################################
## limits.cpus #####################################################
####################################################################

- name: limits.cpus
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    limits:
      cpus: 1
  register: limit_cpu_1

- name: limits.cpus (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    limits:
      cpus: 1
  register: limit_cpu_2

- name: limits.cpus (change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    limits:
      cpus: 0.5
  register: limit_cpu_3

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - limit_cpu_1 is changed
      - limit_cpu_2 is not changed
      - limit_cpu_3 is changed

###################################################################
## limits.memory ##################################################
###################################################################

- name: limits.memory
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    limits:
      memory: 64M
  register: limit_memory_1

- name: limits.memory (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    limits:
      memory: 64M
  register: limit_memory_2

- name: limits.memory (change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    limits:
      memory: 32M
  register: limit_memory_3

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - limit_memory_1 is changed
      - limit_memory_2 is not changed
      - limit_memory_3 is changed

###################################################################
## reservations.cpus ##############################################
###################################################################

- name: reserve_cpu
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    reservations:
      cpus: 1
  register: reserve_cpu_1

- name: reserve_cpu (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    reservations:
      cpus: 1
  register: reserve_cpu_2

- name: reserve_cpu (change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    reservations:
      cpus: 0.5
  register: reserve_cpu_3

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - reserve_cpu_1 is changed
      - reserve_cpu_2 is not changed
      - reserve_cpu_3 is changed

###################################################################
## reservations.memory ############################################
###################################################################

- name: reservations.memory
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    reservations:
      memory: 64M
  register: reserve_memory_1

- name: reservations.memory (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    reservations:
      memory: 64M
  register: reserve_memory_2

- name: reservations.memory (change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    reservations:
      memory: 32M
  register: reserve_memory_3

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - reserve_memory_1 is changed
      - reserve_memory_2 is not changed
      - reserve_memory_3 is changed
