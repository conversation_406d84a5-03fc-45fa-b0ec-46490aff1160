---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

stack_compose_base:
  version: '3'
  services:
    busybox:
      image: "{{ docker_test_image_busybox }}"
      command: sleep 3600

stack_compose_overrides:
  version: '3'
  services:
    busybox:
      environment:
        envvar: value

stack_update_expected_diff: '{"test_stack_busybox": {"TaskTemplate": {"ContainerSpec": {"Env": ["envvar=value"]}}}}'
