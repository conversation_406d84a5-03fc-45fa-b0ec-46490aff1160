---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Get OS version
  shell: uname -r
  register: os_version

- name: Install pre-reqs
  apt:
    name: '{{ docker_prereq_packages }}'
    state: present
    update_cache: true
  notify: cleanup docker

- name: Add gpg key
  shell: curl -fsSL https://download.docker.com/linux/{{ ansible_distribution | lower }}/gpg >key && apt-key add key

- name: Add Docker repo
  apt_repository:
    repo: deb [arch={{ 'arm64' if ansible_architecture == 'aarch64' else 'amd64' }}] https://download.docker.com/linux/{{ ansible_distribution | lower }} {{ ansible_distribution_release }} stable
    state: present

- block:
    - name: Prevent service restart
      copy:
        content: exit 101
        dest: /usr/sbin/policy-rc.d
        backup: true
        mode: '0755'
      register: policy_rc_d

    - name: Install Docker CE
      apt:
        name: '{{ docker_packages if needs_docker_daemon else docker_cli_packages }}'
        state: present

  always:
    - name: Restore /usr/sbin/policy-rc.d (if needed)
      command: mv {{ policy_rc_d.backup_file }} /usr/sbin/policy-rc.d
      when:
        - '"backup_file" in policy_rc_d'

    - name: Remove /usr/sbin/policy-rc.d (if needed)
      file:
        path: /usr/sbin/policy-rc.d
        state: absent
      when:
        - '"backup_file" not in policy_rc_d'
