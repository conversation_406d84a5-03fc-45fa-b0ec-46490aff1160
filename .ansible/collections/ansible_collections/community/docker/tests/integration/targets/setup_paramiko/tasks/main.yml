---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Install paramiko from system repository
  package:
    name: python-paramiko
  become: true
  when:
    - ansible_distribution == 'CentOS' and ansible_distribution_major_version|int <= 7

- name: Install paramiko
  pip:
    name: "paramiko{% if cryptography_version.stdout is version('2.5.0', '<') %}<2.5.0{% endif %}"
    extra_args: "-c {{ remote_constraints }}"
  become: true
