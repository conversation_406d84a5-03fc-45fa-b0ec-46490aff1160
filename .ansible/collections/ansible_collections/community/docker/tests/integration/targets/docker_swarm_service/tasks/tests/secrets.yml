---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Registering container name
  set_fact:
    service_name: "{{ name_prefix ~ '-secrets' }}"
    secret_name_1: "{{ name_prefix ~ '-secret-1' }}"
    secret_name_2: "{{ name_prefix ~ '-secret-2' }}"
    secret_name_3: "{{ name_prefix ~ '-secret-3' }}"

- name: Registering container name
  set_fact:
    secret_names: "{{ secret_names + [secret_name_1, secret_name_2] }}"

- docker_secret:
    name: "{{ secret_name_1 }}"
    data: "secret1"
    state: "present"
  register: "secret_result_1"
  when: docker_py_version is version('2.1.0', '>=')

- docker_secret:
    name: "{{ secret_name_2 }}"
    data: "secret2"
    state: "present"
  register: "secret_result_2"
  when: docker_py_version is version('2.1.0', '>=')

- docker_secret:
    name: "{{ secret_name_3 }}"
    data: "secret3"
    state: "present"
    rolling_versions: true
  register: "secret_result_3"
  when: docker_py_version is version('2.1.0', '>=')

####################################################################
## secrets #########################################################
####################################################################

- name: secrets
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    secrets:
      - secret_id: "{{ secret_result_1.secret_id|default('') }}"
        secret_name: "{{ secret_name_1 }}"
        filename: "/run/secrets/{{ secret_name_1 }}.txt"
  register: secrets_1
  ignore_errors: true

- name: secrets (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    secrets:
      - secret_name: "{{ secret_name_1 }}"
        filename: "/run/secrets/{{ secret_name_1 }}.txt"
  register: secrets_2
  ignore_errors: true

- name: secrets (add)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    secrets:
      - secret_id: "{{ secret_result_1.secret_id|default('') }}"
        secret_name: "{{ secret_name_1 }}"
        filename: "/run/secrets/{{ secret_name_1 }}.txt"
      - secret_name: "{{ secret_name_2 }}"
        filename: "/run/secrets/{{ secret_name_2 }}.txt"
  register: secrets_3
  ignore_errors: true

- name: secrets (add idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    secrets:
      - secret_name: "{{ secret_name_1 }}"
        filename: "/run/secrets/{{ secret_name_1 }}.txt"
      - secret_id: "{{ secret_result_2.secret_id|default('') }}"
        secret_name: "{{ secret_name_2 }}"
        filename: "/run/secrets/{{ secret_name_2 }}.txt"
  register: secrets_4
  ignore_errors: true

- name: secrets (add idempotency no id)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    secrets:
      - secret_name: "{{ secret_name_1 }}"
        filename: "/run/secrets/{{ secret_name_1 }}.txt"
      - secret_name: "{{ secret_name_2 }}"
        filename: "/run/secrets/{{ secret_name_2 }}.txt"
  register: secrets_5
  ignore_errors: true

- name: secrets (order idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    secrets:
      - secret_name: "{{ secret_name_2 }}"
        filename: "/run/secrets/{{ secret_name_2 }}.txt"
      - secret_name: "{{ secret_name_1 }}"
        filename: "/run/secrets/{{ secret_name_1 }}.txt"
  register: secrets_6
  ignore_errors: true

- name: secrets (empty)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    secrets: []
  register: secrets_7
  ignore_errors: true

- name: secrets (empty idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    secrets: []
  register: secrets_8
  ignore_errors: true

- name: rolling secrets
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    secrets:
      - secret_name: "{{ secret_name_3 }}_v1"
        filename: "/run/secrets/{{ secret_name_3 }}.txt"
  register: secrets_9
  ignore_errors: true

- name: update rolling secret
  docker_secret:
    name: "{{ secret_name_3 }}"
    data: "newsecret3"
    state: "present"
    rolling_versions: true
  register: secrets_10
  when: docker_py_version is version('2.1.0', '>=')
  ignore_errors: true

- name: rolling secrets service update
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    secrets:
      - secret_name: "{{ secret_name_3 }}_v2"
        filename: "/run/secrets/{{ secret_name_3 }}.txt"
  register: secrets_11
  ignore_errors: true

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - secrets_1 is changed
      - secrets_2 is not changed
      - secrets_3 is changed
      - secrets_4 is not changed
      - secrets_5 is not changed
      - secrets_6 is not changed
      - secrets_7 is changed
      - secrets_8 is not changed
      - secrets_9 is changed
      - secrets_10 is not failed
      - secrets_11 is changed
  when: docker_py_version is version('2.4.0', '>=')
- assert:
    that:
      - secrets_1 is failed
      - "'Minimum version required' in secrets_1.msg"
  when: docker_py_version is version('2.4.0', '<')

####################################################################
## secrets (uid) ###################################################
####################################################################

- name: secrets (uid int)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    secrets:
      - secret_id: "{{ secret_result_1.secret_id|default('') }}"
        secret_name: "{{ secret_name_1 }}"
        uid: 1000
  register: secrets_1
  ignore_errors: true

- name: secrets (uid int idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    secrets:
      - secret_id: "{{ secret_result_1.secret_id|default('') }}"
        secret_name: "{{ secret_name_1 }}"
        uid: 1000
  register: secrets_2
  ignore_errors: true

- name: secrets (uid int change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    secrets:
      - secret_id: "{{ secret_result_1.secret_id|default('') }}"
        secret_name: "{{ secret_name_1 }}"
        uid: 1002
  register: secrets_3
  ignore_errors: true

- name: secrets (uid str)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    secrets:
      - secret_id: "{{ secret_result_1.secret_id|default('') }}"
        secret_name: "{{ secret_name_1 }}"
        uid: "1001"
  register: secrets_4
  ignore_errors: true

- name: secrets (uid str idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    secrets:
      - secret_id: "{{ secret_result_1.secret_id|default('') }}"
        secret_name: "{{ secret_name_1 }}"
        uid: "1001"
  register: secrets_5
  ignore_errors: true

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - secrets_1 is changed
      - secrets_2 is not changed
      - secrets_3 is changed
      - secrets_4 is changed
      - secrets_5 is not changed
  when: docker_py_version is version('2.4.0', '>=')
- assert:
    that:
      - secrets_1 is failed
      - "'Minimum version required' in secrets_1.msg"
  when: docker_py_version is version('2.4.0', '<')

####################################################################
## secrets (gid) ###################################################
####################################################################

- name: secrets (gid int)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    secrets:
      - secret_id: "{{ secret_result_1.secret_id|default('') }}"
        secret_name: "{{ secret_name_1 }}"
        gid: 1001
  register: secrets_1
  ignore_errors: true

- name: secrets (gid int idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    secrets:
      - secret_id: "{{ secret_result_1.secret_id|default('') }}"
        secret_name: "{{ secret_name_1 }}"
        gid: 1001
  register: secrets_2
  ignore_errors: true

- name: secrets (gid int change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    secrets:
      - secret_id: "{{ secret_result_1.secret_id|default('') }}"
        secret_name: "{{ secret_name_1 }}"
        gid: 1002
  register: secrets_3
  ignore_errors: true

- name: secrets (gid str)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    secrets:
      - secret_id: "{{ secret_result_1.secret_id|default('') }}"
        secret_name: "{{ secret_name_1 }}"
        gid: "1003"
  register: secrets_4
  ignore_errors: true

- name: secrets (gid str idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    secrets:
      - secret_id: "{{ secret_result_1.secret_id|default('') }}"
        secret_name: "{{ secret_name_1 }}"
        gid: "1003"
  register: secrets_5
  ignore_errors: true

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - secrets_1 is changed
      - secrets_2 is not changed
      - secrets_3 is changed
      - secrets_4 is changed
      - secrets_5 is not changed
  when: docker_py_version is version('2.4.0', '>=')
- assert:
    that:
      - secrets_1 is failed
      - "'Minimum version required' in secrets_1.msg"
  when: docker_py_version is version('2.4.0', '<')

####################################################################
## secrets (mode) ##################################################
####################################################################

- name: secrets (mode)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    secrets:
      - secret_id: "{{ secret_result_1.secret_id|default('') }}"
        secret_name: "{{ secret_name_1 }}"
        mode: "0600"
  register: secrets_1
  ignore_errors: true

- name: secrets (mode idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    secrets:
      - secret_id: "{{ secret_result_1.secret_id|default('') }}"
        secret_name: "{{ secret_name_1 }}"
        mode: "0600"
  register: secrets_2
  ignore_errors: true

- name: secrets (mode change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    secrets:
      - secret_id: "{{ secret_result_1.secret_id|default('') }}"
        secret_name: "{{ secret_name_1 }}"
        mode: "0777"
  register: secrets_3
  ignore_errors: true

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - secrets_1 is changed
      - secrets_2 is not changed
      - secrets_3 is changed
  when: docker_py_version is version('2.4.0', '>=')
- assert:
    that:
      - secrets_1 is failed
      - "'Minimum version required' in secrets_1.msg"
  when: docker_py_version is version('2.4.0', '<')

####################################################################
####################################################################
####################################################################

- name: Delete secrets
  docker_secret:
    name: "{{ secret_name }}"
    state: absent
    force: true
  loop:
    - "{{ secret_name_1 }}"
    - "{{ secret_name_2 }}"
    - "{{ secret_name_3 }}"
  loop_control:
    loop_var: secret_name
  ignore_errors: true
  when: docker_py_version is version('2.1.0', '>=')
