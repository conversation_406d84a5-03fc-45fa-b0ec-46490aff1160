---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- debug:
    msg: Running tests/remote-addr-pool.yml

####################################################################
## default_addr_pool ###############################################
####################################################################

- name: default_addr_pool
  docker_swarm:
    state: present
    default_addr_pool:
      - "2.0.0.0/16"
  diff: true
  register: output_1
  ignore_errors: true

- name: default_addr_pool (idempotent)
  docker_swarm:
    state: present
    default_addr_pool:
      - "2.0.0.0/16"
  diff: true
  register: output_2
  ignore_errors: true

- name: assert default_addr_pool
  assert:
    that:
      - 'output_1 is changed'
      - 'output_2 is not changed'
      - 'output_2.swarm_facts.DefaultAddrPool == ["2.0.0.0/16"]'
  when:
    - docker_api_version is version('1.39', '>=')
    - docker_py_version is version('4.0.0', '>=')

- name: assert default_addr_pool failed when unsupported
  assert:
    that:
      - 'output_1 is failed'
      - "'Minimum version required' in output_1.msg"
  when: docker_api_version is version('1.39', '<') or
        docker_py_version is version('4.0.0', '<')

####################################################################
## subnet_size #####################################################
####################################################################
- name: Leave swarm
  docker_swarm:
    state: absent
    force: true
    default_addr_pool:
      - "2.0.0.0/16"
  diff: true

- name: subnet_size
  docker_swarm:
    state: present
    force: true
    subnet_size: 26
  diff: true
  register: output_1
  ignore_errors: true

- name: subnet_size (idempotent)
  docker_swarm:
    state: present
    subnet_size: 26
  diff: true
  register: output_2
  ignore_errors: true

- name: assert subnet_size
  assert:
    that:
      - 'output_1 is changed'
      - 'output_2 is not changed'
      - 'output_2.swarm_facts.SubnetSize == 26'
  when:
    - docker_api_version is version('1.39', '>=')
    - docker_py_version is version('4.0.0', '>=')

- name: assert subnet_size failed when unsupported
  assert:
    that:
      - output_1 is failed
      - "'Minimum version required' in output_1.msg"
  when: docker_api_version is version('1.39', '<') or
        docker_py_version is version('4.0.0', '<')

- include_tasks: cleanup.yml
