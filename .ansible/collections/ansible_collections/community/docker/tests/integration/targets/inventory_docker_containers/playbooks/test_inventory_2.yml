---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- hosts: 127.0.0.1
  connection: local  # otherwise Ansible will complain that it cannot connect via ssh to 127.0.0.1:22
  gather_facts: false
  tasks:
    - name: Show all groups
      debug:
        var: groups
    - name: Load variables
      include_vars: ../../setup_docker/vars/main.yml
    - name: Make sure that the expected groups are there
      assert:
        that:
          - groups.all | length >= 2
          - groups.ungrouped | length >= 0
          - groups.running | length >= 2
          - groups.stopped | length >= 0
          - groups['image_' ~ docker_test_image_alpine] | length == 2
          - groups['ansible-docker-test-docker-inventory-container-1'] | length == 1
          - groups['ansible-docker-test-docker-inventory-container-2'] | length == 1
          - groups['unix://var/run/docker.sock'] | length >= 2
          - groups | length >= 12
          # The four additional groups are IDs and short IDs of the containers.
          # When the integration tests are run inside a docker container, there
          # will be more groups (for the additional container(s)).

- hosts: all
  # We don't really want to connect to the nodes, since we have no SSH daemon running on them
  connection: local
  vars:
    ansible_python_interpreter: "{{ ansible_playbook_python }}"
  gather_facts: false
  tasks:
    - name: Show all variables
      debug:
        var: hostvars[inventory_hostname]
    - name: Make sure SSH is set up
      assert:
        that:
          - ansible_ssh_host == '*******'
          - ansible_ssh_port == docker_networksettings.Ports['22/tcp'][0].HostPort
      when:
        # When the integration tests are run inside a docker container, there
        # will be other containers.
        - inventory_hostname.startswith('ansible-docker-test-docker-inventory-container-')
    - name: Write labels into file
      copy:
        dest: "/tmp/{{ inventory_hostname }}-labels.txt"
        content: |-
          {{ docker_config.Labels }}
      delegate_to: localhost
      when:
        # When the integration tests are run inside a docker container, there
        # will be other containers.
        - inventory_hostname.startswith('ansible-docker-test-docker-inventory-container-')
