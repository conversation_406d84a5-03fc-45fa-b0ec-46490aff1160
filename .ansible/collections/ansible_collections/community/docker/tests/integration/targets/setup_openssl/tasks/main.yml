---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

- name: Include OS-specific variables
  include_vars: '{{ ansible_os_family }}.yml'
  when: not ansible_os_family == "Darwin"

- name: Install cryptography (Python 3)
  become: true
  package:
    name: '{{ cryptography_package_name_python3 }}'
  when: ansible_os_family != 'Darwin' and ansible_python_version is version('3.0', '>=')

- name: Install cryptography (Python 2)
  become: true
  package:
    name: '{{ cryptography_package_name }}'
  when: ansible_os_family != 'Darwin' and ansible_python_version is version('3.0', '<')

- name: Install cryptography (Darwin, and potentially upgrade for other OSes)
  become: true
  pip:
    name: cryptography>=1.3.0
    extra_args: "-c {{ remote_constraints }}"

- name: Register cryptography version
  command: "{{ ansible_python.executable }} -c 'import cryptography; print(cryptography.__version__)'"
  register: cryptography_version
