---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

- name: Run Docker Swarm tests
  when:
    - docker_py_version is version('1.10.0', '>=')
    - docker_api_version is version('1.25', '>=')

  block:
    - include_tasks: run-test.yml
      with_fileglob:
        - "tests/*.yml"
      loop_control:
        loop_var: test_name

  always:
    - import_tasks: cleanup.yml

- fail:
    msg: "Too old docker / docker-py version to run docker_swarm tests!"
  when:
    - not(docker_py_version is version('1.10.0', '>=') and docker_api_version is version('1.25', '>='))
    - (ansible_distribution != 'CentOS' or ansible_distribution_major_version|int > 6)
