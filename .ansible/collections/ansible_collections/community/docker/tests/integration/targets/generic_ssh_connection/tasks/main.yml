---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

- name: Get docker daemon information directly
  docker_host_info:
  register: output

- name: Make sure we got information
  assert:
    that:
      - 'output.host_info.Name is string'
      - 'output.containers is not defined'
      - 'output.networks is not defined'
      - 'output.volumes is not defined'
      - 'output.images is not defined'
      - 'output.disk_usage is not defined'

- name: Show contents of ~/.ssh
  command: ls -lah ~/.ssh
  ignore_errors: true

- name: Recover home directory on remote
  command: echo $HOME
  register: remote_home

- name: Print remote home directory
  debug:
    var: remote_home.stdout

- name: Create SSH config
  copy:
    dest: "{{ remote_home.stdout }}/.ssh/config"
    mode: '0600'
    content: |
      Host localhost
      User root
      IdentityFile ~/.ssh/id_rsa

- name: Get docker daemon information via ssh (paramiko) to localhost
  docker_host_info:
    docker_host: "ssh://root@localhost"
  register: output
  ignore_errors: true

- name: Make sure we got information
  assert:
    that:
      - 'output.host_info.Name is string'
      - 'output.containers is not defined'
      - 'output.networks is not defined'
      - 'output.volumes is not defined'
      - 'output.images is not defined'
      - 'output.disk_usage is not defined'
  when: output is succeeded or 'Failed to import the required Python library (paramiko)' not in output.msg
  # Sometimes paramiko being installed isn't enough: importing it can fail
  # due to 'ImportError: No module named x25519' when it executes
  # `from cryptography.hazmat.primitives.asymmetric.x25519 import ...`.

- name: Get docker daemon information via ssh (OpenSSH) to localhost
  docker_host_info:
    docker_host: "ssh://root@localhost"
    use_ssh_client: true
  register: output

- name: Make sure we got information
  assert:
    that:
      - output is succeeded
      - 'output.host_info.Name is string'
      - 'output.containers is not defined'
      - 'output.networks is not defined'
      - 'output.volumes is not defined'
      - 'output.images is not defined'
      - 'output.disk_usage is not defined'
