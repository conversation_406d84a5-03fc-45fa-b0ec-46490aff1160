---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Registering plugin name
  set_fact:
    plugin_name: "{{ name_prefix }}"

- name: Registering container name
  set_fact:
    plugin_names: "{{ plugin_names + [plugin_name] }}"

############ basic test ############
####################################

- name: Create a plugin (check mode)
  docker_plugin:
    plugin_name: "{{ plugin_name }}"
    state: present
  register: create_1_check
  check_mode: true

- name: Create a plugin
  docker_plugin:
    plugin_name: "{{ plugin_name }}"
    state: present
  register: create_1

- name: Create a plugin (Idempotent, check mode)
  docker_plugin:
    plugin_name: "{{ plugin_name }}"
    state: present
  register: create_2_check
  check_mode: true

- name: Create a plugin (Idempotent)
  docker_plugin:
    plugin_name: "{{ plugin_name }}"
    state: present
  register: create_2

- name: Enable a plugin (check mode)
  docker_plugin:
    plugin_name: "{{ plugin_name }}"
    state: enable
  register: create_3_check
  check_mode: true

- name: Enable a plugin
  docker_plugin:
    plugin_name: "{{ plugin_name }}"
    state: enable
  register: create_3

- name: Enable a plugin (Idempotent, check mode)
  docker_plugin:
    plugin_name: "{{ plugin_name }}"
    state: enable
  register: create_4_check
  check_mode: true

- name: Enable a plugin (Idempotent)
  docker_plugin:
    plugin_name: "{{ plugin_name }}"
    state: enable
  register: create_4

- name: Disable a plugin (check mode)
  docker_plugin:
    plugin_name: "{{ plugin_name }}"
    state: disable
  register: absent_1_check
  check_mode: true

- name: Disable a plugin
  docker_plugin:
    plugin_name: "{{ plugin_name }}"
    state: disable
  register: absent_1

- name: Disable a plugin (Idempotent, check mode)
  docker_plugin:
    plugin_name: "{{ plugin_name }}"
    state: disable
  register: absent_2_check
  check_mode: true

- name: Disable a plugin (Idempotent)
  docker_plugin:
    plugin_name: "{{ plugin_name }}"
    state: disable
  register: absent_2

- name: Remove a plugin (check mode)
  docker_plugin:
    plugin_name: "{{ plugin_name }}"
    state: absent
  register: absent_3_check
  check_mode: true

- name: Remove a plugin
  docker_plugin:
    plugin_name: "{{ plugin_name }}"
    state: absent
  register: absent_3

- name: Remove a plugin (Idempotent, check mode)
  docker_plugin:
    plugin_name: "{{ plugin_name }}"
    state: absent
  register: absent_4_check
  check_mode: true

- name: Remove a plugin (Idempotent)
  docker_plugin:
    plugin_name: "{{ plugin_name }}"
    state: absent
  register: absent_4

- name: Cleanup
  docker_plugin:
    plugin_name: "{{ plugin_name }}"
    state: absent
    force_remove: true

- assert:
    that:
      - create_1_check is changed
      - create_1 is changed
      - create_2_check is not changed
      - create_2 is not changed
      - create_3_check is changed
      - create_3 is changed
      - create_4_check is not changed
      - create_4 is not changed
      - absent_1_check is changed
      - absent_1 is changed
      - absent_2_check is not changed
      - absent_2 is not changed
      - absent_3_check is changed
      - absent_3 is changed
      - absent_4_check is not changed
      - absent_4 is not changed

############ Plugin_Options ############
########################################

- name: Install a plugin with options
  docker_plugin:
    plugin_name: "{{ plugin_name }}"
    plugin_options:
      DEBUG: '1'
    state: present
  register: create_1

- name: Install a plugin with options (idempotent)
  docker_plugin:
    plugin_name: "{{ plugin_name }}"
    plugin_options:
      DEBUG: '1'
    state: present
  register: create_2

- name: Install a plugin with different options
  docker_plugin:
    plugin_name: "{{ plugin_name }}"
    plugin_options:
      DEBUG: '0'
    state: present
  register: update_1

- name: Install a plugin with different options (idempotent)
  docker_plugin:
    plugin_name: "{{ plugin_name }}"
    plugin_options:
      DEBUG: '0'
    state: present
  register: update_2

- name: Cleanup
  docker_plugin:
    plugin_name: "{{ plugin_name }}"
    state: absent
    force_remove: true

- assert:
    that:
      - create_1 is changed
      - create_2 is not changed
      - update_1 is changed
      - update_2 is not changed
