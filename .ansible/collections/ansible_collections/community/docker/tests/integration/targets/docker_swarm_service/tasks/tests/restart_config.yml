---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Registering service name
  set_fact:
    service_name: "{{ name_prefix ~ '-restart_config' }}"

- name: Registering service name
  set_fact:
    service_names: "{{ service_names + [service_name] }}"

###################################################################
## restart_config.condition #######################################
###################################################################

- name: restart_config.condition
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    restart_config:
      condition: "on-failure"
  register: restart_policy_1

- name: restart_config.condition (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    restart_config:
      condition: "on-failure"
  register: restart_policy_2

- name: restart_config.condition (change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    restart_config:
      condition: "any"
  register: restart_policy_3

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - restart_policy_1 is changed
      - restart_policy_2 is not changed
      - restart_policy_3 is changed

###################################################################
## restart_config.max_attempts ####################################
###################################################################

- name: restart_config.max_attempts
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    restart_config:
      max_attempts: 1
  register: restart_policy_attempts_1

- name: restart_config.max_attempts (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    restart_config:
      max_attempts: 1
  register: restart_policy_attempts_2

- name: restart_config.max_attempts (change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    restart_config:
      max_attempts: 2
  register: restart_policy_attempts_3

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - restart_policy_attempts_1 is changed
      - restart_policy_attempts_2 is not changed
      - restart_policy_attempts_3 is changed

###################################################################
## restart_config.delay ###########################################
###################################################################

- name: restart_config.delay
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    restart_config:
      delay: 5s
  register: restart_policy_delay_1

- name: restart_config.delay (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    restart_config:
      delay: 5s
  register: restart_policy_delay_2

- name: restart_config.delay (change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    restart_config:
      delay: 10s
  register: restart_policy_delay_3

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - restart_policy_delay_1 is changed
      - restart_policy_delay_2 is not changed
      - restart_policy_delay_3 is changed

###################################################################
## restart_config.window ##########################################
###################################################################

- name: restart_config.window
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    restart_config:
      window: 10s
  register: restart_policy_window_1

- name: restart_config.window (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    restart_config:
      window: 10s
  register: restart_policy_window_2

- name: restart_config.window (change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    restart_config:
      window: 20s
  register: restart_policy_window_3

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - restart_policy_window_1 is changed
      - restart_policy_window_2 is not changed
      - restart_policy_window_3 is changed
