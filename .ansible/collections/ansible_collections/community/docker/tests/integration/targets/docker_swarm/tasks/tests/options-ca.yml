---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- debug:
    msg: Running tests/options-ca.yml
- name: options-ca
  when: cryptography_version.stdout is version('1.6', '>=')
  block:
    - name: Generate privatekey
      loop:
        - key1
        - key2
      loop_control:
        loop_var: key
      community.crypto.openssl_privatekey:
        path: '{{ remote_tmp_dir }}/ansible_{{ key }}.key'
        size: 2048
        mode: '0666'
    - name: Generate CSR
      loop:
        - key1
        - key2
      loop_control:
        loop_var: key
      community.crypto.openssl_csr:
        path: '{{ remote_tmp_dir }}/ansible_{{ key }}.csr'
        privatekey_path: '{{ remote_tmp_dir }}/ansible_{{ key }}.key'
        basic_constraints:
          - CA:TRUE
        key_usage:
          - keyCertSign
    - name: Generate self-signed certificate
      loop:
        - key1
        - key2
      loop_control:
        loop_var: key
      community.crypto.x509_certificate:
        path: '{{ remote_tmp_dir }}/ansible_{{ key }}.pem'
        privatekey_path: '{{ remote_tmp_dir }}/ansible_{{ key }}.key'
        csr_path: '{{ remote_tmp_dir }}/ansible_{{ key }}.csr'
        provider: selfsigned
    - name: Load certificates
      slurp:
        src: '{{ remote_tmp_dir }}/{{ item }}'
      loop:
        - ansible_key1.pem
        - ansible_key2.pem
      register: ansible_certificates
    - name: Load certificate keys
      slurp:
        src: '{{ remote_tmp_dir }}/{{ item }}'
      loop:
        - ansible_key1.key
        - ansible_key2.key
      register: ansible_keys
    - name: signing_ca_cert and signing_ca_key (check mode)
      docker_swarm:
        advertise_addr: '{{ansible_default_ipv4.address | default("127.0.0.1")}}'
        state: present
        signing_ca_cert: '{{ ansible_certificates.results[0].content | b64decode }}'
        signing_ca_key: '{{ ansible_keys.results[0].content | b64decode }}'
        timeout: 120
      check_mode: true
      diff: true
      register: output_1
      ignore_errors: true
    - name: signing_ca_cert and signing_ca_key
      docker_swarm:
        advertise_addr: '{{ansible_default_ipv4.address | default("127.0.0.1")}}'
        state: present
        signing_ca_cert: '{{ ansible_certificates.results[0].content | b64decode }}'
        signing_ca_key: '{{ ansible_keys.results[0].content | b64decode }}'
        timeout: 120
      diff: true
      register: output_2
      ignore_errors: true
    - name: Private key
      debug: msg="{{ ansible_keys.results[0].content | b64decode }}"
    - name: Cert
      debug: msg="{{ ansible_certificates.results[0].content | b64decode }}"
    - docker_swarm_info: null
      register: output
      ignore_errors: true
    - debug: var=output
    - name: signing_ca_cert and signing_ca_key (change, check mode)
      docker_swarm:
        state: present
        signing_ca_cert: '{{ ansible_certificates.results[1].content | b64decode }}'
        signing_ca_key: '{{ ansible_keys.results[1].content | b64decode }}'
        timeout: 120
      check_mode: true
      diff: true
      register: output_5
      ignore_errors: true
    - name: signing_ca_cert and signing_ca_key (change)
      docker_swarm:
        state: present
        signing_ca_cert: '{{ ansible_certificates.results[1].content | b64decode }}'
        signing_ca_key: '{{ ansible_keys.results[1].content | b64decode }}'
        timeout: 120
      diff: true
      register: output_6
      ignore_errors: true
    - name: assert signing_ca_cert and signing_ca_key
      assert:
        that:
          - output_1 is changed
          - '(output_1.actions[0] | regex_search("New Swarm cluster created: ")) is truthy'
          - output_1.diff.before is defined
          - output_1.diff.after is defined
          - output_2 is changed
          - '(output_2.actions[0] | regex_search("New Swarm cluster created: ")) is truthy'
          - output_2.diff.before is defined
          - output_2.diff.after is defined
          - output_5 is changed
          - output_5.actions[0] == "Swarm cluster updated"
          - output_5.diff.before is defined
          - output_5.diff.after is defined
          - output_6 is changed
          - output_6.actions[0] == "Swarm cluster updated"
          - output_6.diff.before is defined
          - output_6.diff.after is defined
      when: docker_py_version is version('2.6.0', '>=')
    - assert:
        that:
          - output_1 is failed
          - ('version is ' ~ docker_py_version ~ ' ') in output_1.msg
          - '"Minimum version required is 2.6.0 " in output_1.msg'
      when: docker_py_version is version('2.6.0', '<')
    - include_tasks: cleanup.yml
