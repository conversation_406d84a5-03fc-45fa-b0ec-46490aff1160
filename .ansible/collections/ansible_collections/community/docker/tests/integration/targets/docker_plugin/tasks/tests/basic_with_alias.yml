---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Register plugin name and alias
  set_fact:
    plugin_name: "{{ name_prefix }}"
    alias: "test"

- name: Create a plugin with an alias
  docker_plugin:
    plugin_name: "{{ plugin_name }}"
    alias: "{{ alias }}"
    state: present
  register: create_1

- name: Create a plugin  with an alias (Idempotent)
  docker_plugin:
    plugin_name: "{{ plugin_name }}"
    alias: "{{ alias }}"
    state: present
  register: create_2

- name: Enable a plugin with an alias
  docker_plugin:
    plugin_name: "{{ plugin_name }}"
    alias: "{{ alias }}"
    state: enable
  register: create_3

- name: Enable a plugin with an alias (Idempotent)
  docker_plugin:
    plugin_name: "{{ plugin_name }}"
    alias: "{{ alias }}"
    state: enable
  register: create_4

- name: Disable a plugin with an alias
  docker_plugin:
    plugin_name: "{{ plugin_name }}"
    alias: "{{ alias }}"
    state: disable
  register: absent_1

- name: Disable a plugin with an alias (Idempotent)
  docker_plugin:
    plugin_name: "{{ plugin_name }}"
    alias: "{{ alias }}"
    state: disable
  register: absent_2

- name: Remove a plugin with an alias
  docker_plugin:
    plugin_name: "{{ plugin_name }}"
    alias: "{{ alias }}"
    state: absent
  register: absent_3

- name: Remove a plugin with an alias (Idempotent)
  docker_plugin:
    plugin_name: "{{ plugin_name }}"
    alias: "{{ alias }}"
    state: absent
  register: absent_4

- assert:
    that:
      - create_1 is changed
      - create_2 is not changed
      - create_3 is changed
      - create_4 is not changed
      - absent_1 is changed
      - absent_2 is not changed
      - absent_3 is changed
      - absent_4 is not changed

- name: Cleanup plugin with an alias
  docker_plugin:
    plugin_name: "{{ plugin_name }}"
    alias: "{{ alias }}"
    state: absent
    force_remove: true
