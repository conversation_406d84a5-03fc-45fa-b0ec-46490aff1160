---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Create random name prefix
  set_fact:
    name_prefix: "vieux/sshfs"
    plugin_names: []

- debug:
    msg: "Using name prefix {{ name_prefix }}"

- name: Check whether /dev/fuse exists
  stat:
    path: /dev/fuse
  register: dev_fuse_stat

- block:
    - include_tasks: run-test.yml
      with_fileglob:
        - "tests/*.yml"
      loop_control:
        loop_var: test_name

  always:
    - name: "Make sure plugin is removed"
      docker_plugin:
        plugin_name: "{{ item }}"
        state: absent
      with_items: "{{ plugin_names }}"

  when: docker_api_version is version('1.25', '>=') and dev_fuse_stat.stat.exists

- fail: msg="Too old docker / docker-py version to run docker_plugin tests!"
  when: not(docker_api_version is version('1.25', '>=')) and (ansible_distribution != 'CentOS' or ansible_distribution_major_version|int > 6)
