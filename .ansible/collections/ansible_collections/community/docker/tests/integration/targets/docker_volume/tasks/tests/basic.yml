---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Registering volume name
  set_fact:
    vname: "{{ name_prefix ~ '-basic' }}"
- name: Registering container name
  set_fact:
    vnames: "{{ vnames + [vname] }}"

####################################################################
## basic ###########################################################
####################################################################

- name: Create a volume
  docker_volume:
    name: "{{ vname }}"
  register: create_1

- name: Create a volume (idempotency)
  docker_volume:
    name: "{{ vname }}"
  register: create_2

- name: "Create a volume (recreate: options-changed)"
  docker_volume:
    name: "{{ vname }}"
    recreate: options-changed
  register: create_3

- name: "Create a volume (recreate: always)"
  docker_volume:
    name: "{{ vname }}"
    recreate: always
  register: create_4

- name: Remove a volume
  docker_volume:
    name: "{{ vname }}"
    state: absent
  register: absent_1

- name: Remove a volume (idempotency)
  docker_volume:
    name: "{{ vname }}"
    state: absent
  register: absent_2

- assert:
    that:
      - create_1 is changed
      - create_2 is not changed
      - create_3 is not changed
      - create_4 is changed
      - absent_1 is changed
      - absent_2 is not changed

####################################################################
## driver_options ##################################################
####################################################################

- name: Create a volume with options
  docker_volume:
    name: "{{ vname }}"
    driver: local
    driver_options:
      type: tempfs
      device: tmpfs
      o: size=100m,uid=1000
  register: driver_options_1

- name: Create a volume with options (idempotency)
  docker_volume:
    name: "{{ vname }}"
    driver: local
    driver_options:
      type: tempfs
      device: tmpfs
      o: size=100m,uid=1000
  register: driver_options_2

- name: Create a volume with options (changed)
  docker_volume:
    name: "{{ vname }}"
    driver: local
    driver_options:
      type: tempfs
      device: tmpfs
      o: size=200m,uid=1000
  register: driver_options_3

- name: "Create a volume with options (changed, recreate: options-changed)"
  docker_volume:
    name: "{{ vname }}"
    driver: local
    driver_options:
      type: tempfs
      device: tmpfs
      o: size=200m,uid=1000
    recreate: options-changed
  register: driver_options_4

- name: Cleanup
  docker_volume:
    name: "{{ vname }}"
    state: absent

- assert:
    that:
      - driver_options_1 is changed
      - driver_options_2 is not changed
      - driver_options_3 is not changed
      - driver_options_4 is changed

####################################################################
## labels ##########################################################
####################################################################

- name: Create a volume with an invalid label
  docker_volume:
    name: "{{ vname }}"
    labels:
      foo: 1.0
  register: driver_labels_invalid
  ignore_errors: true

- name: Create a volume with labels
  docker_volume:
    name: "{{ vname }}"
    labels:
      ansible.test.1: hello
      ansible.test.2: world
  register: driver_labels_1

- name: Create a volume with labels (idempotency)
  docker_volume:
    name: "{{ vname }}"
    labels:
      ansible.test.2: world
      ansible.test.1: hello
  register: driver_labels_2

- name: Create a volume with labels (less)
  docker_volume:
    name: "{{ vname }}"
    labels:
      ansible.test.1: hello
  register: driver_labels_3

- name: "Create a volume with labels (less, recreate: options-changed)"
  docker_volume:
    name: "{{ vname }}"
    labels:
      ansible.test.1: hello
    recreate: options-changed
  register: driver_labels_4

- name: Create a volume with labels (more)
  docker_volume:
    name: "{{ vname }}"
    labels:
      ansible.test.1: hello
      ansible.test.3: ansible
  register: driver_labels_5

- name: "Create a volume with labels (more, recreate: options-changed)"
  docker_volume:
    name: "{{ vname }}"
    labels:
      ansible.test.1: hello
      ansible.test.3: ansible
    recreate: options-changed
  register: driver_labels_6

- name: Cleanup
  docker_volume:
    name: "{{ vname }}"
    state: absent

- assert:
    that:
      - driver_labels_invalid is failed
      - driver_labels_invalid.msg == "The value 1.0 for 'foo' of labels is not a string or something than can be safely converted to a string!"
      - driver_labels_1 is changed
      - driver_labels_2 is not changed
      - driver_labels_3 is not changed
      - driver_labels_4 is not changed
      - driver_labels_5 is not changed
      - driver_labels_6 is changed
