---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Registering service name
  set_fact:
    service_name: "{{ name_prefix ~ '-options' }}"

- name: Registering service name
  set_fact:
    service_names: "{{ service_names + [service_name] }}"

####################################################################
## args ############################################################
####################################################################

- name: args
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    args:
      - sleep
      - "3600"
  register: args_1

- name: args (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    args:
      - sleep
      - "3600"
  register: args_2

- name: args (change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    args:
      - sleep
      - "3400"
  register: args_3

- name: args (empty)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    args: []
  register: args_4

- name: args (empty idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    args: []
  register: args_5

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - args_1 is changed
      - args_2 is not changed
      - args_3 is changed
      - args_4 is changed
      - args_5 is not changed

####################################################################
## command #########################################################
####################################################################

- name: command
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
  register: command_1

- name: command (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
  register: command_2

- name: command (less parameters)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -c "sleep 10m"'
  register: command_3

- name: command (as list)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command:
      - "/bin/sh"
      - "-c"
      - "sleep 10m"
  register: command_4

- name: command (empty)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: []
  register: command_5

- name: command (empty idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: []
  register: command_6

- name: command (string failure)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: true
  register: command_7
  ignore_errors: true

- name: command (list failure)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command:
      - "/bin/sh"
      - true
  register: command_8
  ignore_errors: true

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - command_1 is changed
      - command_2 is not changed
      - command_3 is changed
      - command_4 is not changed
      - command_5 is changed
      - command_6 is not changed
      - command_7 is failed
      - command_8 is failed

####################################################################
## container_labels ################################################
####################################################################

- name: container_labels
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    container_labels:
      test_1: "1"
      test_2: "2"
  register: container_labels_1

- name: container_labels (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    container_labels:
      test_1: "1"
      test_2: "2"
  register: container_labels_2

- name: container_labels (change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    container_labels:
      test_1: "1"
      test_2: "3"
  register: container_labels_3

- name: container_labels (empty)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    container_labels: {}
  register: container_labels_4

- name: container_labels (empty idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    container_labels: {}
  register: container_labels_5

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - container_labels_1 is changed
      - container_labels_2 is not changed
      - container_labels_3 is changed
      - container_labels_4 is changed
      - container_labels_5 is not changed

####################################################################
## dns #############################################################
####################################################################

- name: dns
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    dns:
      - 1.1.1.1
      - 8.8.8.8
  register: dns_1
  ignore_errors: true

- name: dns (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    dns:
      - 1.1.1.1
      - 8.8.8.8
  register: dns_2
  ignore_errors: true

- name: dns_servers (changed order)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    dns:
      - 8.8.8.8
      - 1.1.1.1
  register: dns_3
  ignore_errors: true

- name: dns_servers (changed elements)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    dns:
      - 8.8.8.8
      - 9.9.9.9
  register: dns_4
  ignore_errors: true

- name: dns_servers (empty)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    dns: []
  register: dns_5
  ignore_errors: true

- name: dns_servers (empty idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    dns: []
  register: dns_6
  ignore_errors: true

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - dns_1 is changed
      - dns_2 is not changed
      - dns_3 is changed
      - dns_4 is changed
      - dns_5 is changed
      - dns_6 is not changed
  when: docker_py_version is version('2.6.0', '>=')
- assert:
    that:
      - dns_1 is failed
      - "'Minimum version required' in dns_1.msg"
  when: docker_py_version is version('2.6.0', '<')

####################################################################
## dns_options #####################################################
####################################################################

- name: dns_options
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    dns_options:
      - "timeout:10"
      - rotate
  register: dns_options_1
  ignore_errors: true

- name: dns_options (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    dns_options:
      - "timeout:10"
      - rotate
  register: dns_options_2
  ignore_errors: true

- name: dns_options (change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    dns_options:
      - "timeout:10"
      - no-check-names
  register: dns_options_3
  ignore_errors: true

- name: dns_options (order idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    dns_options:
      - no-check-names
      - "timeout:10"
  register: dns_options_4
  ignore_errors: true

- name: dns_options (empty)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    dns_options: []
  register: dns_options_5
  ignore_errors: true

- name: dns_options (empty idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    dns_options: []
  register: dns_options_6
  ignore_errors: true

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - dns_options_1 is changed
      - dns_options_2 is not changed
      - dns_options_3 is changed
      - dns_options_4 is not changed
      - dns_options_5 is changed
      - dns_options_6 is not changed
  when: docker_py_version is version('2.6.0', '>=')
- assert:
    that:
      - dns_options_1 is failed
      - "'Minimum version required' in dns_options_1.msg"
  when: docker_py_version is version('2.6.0', '<')

####################################################################
## dns_search ######################################################
####################################################################

- name: dns_search
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    dns_search:
      - example.com
      - example.org
  register: dns_search_1
  ignore_errors: true

- name: dns_search (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    dns_search:
      - example.com
      - example.org
  register: dns_search_2
  ignore_errors: true

- name: dns_search (different order)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    dns_search:
      - example.org
      - example.com
  register: dns_search_3
  ignore_errors: true

- name: dns_search (changed elements)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    dns_search:
      - ansible.com
      - example.com
  register: dns_search_4
  ignore_errors: true

- name: dns_search (empty)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    dns_search: []
  register: dns_search_5
  ignore_errors: true

- name: dns_search (empty idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    dns_search: []
  register: dns_search_6
  ignore_errors: true

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - dns_search_1 is changed
      - dns_search_2 is not changed
      - dns_search_3 is changed
      - dns_search_4 is changed
      - dns_search_5 is changed
      - dns_search_6 is not changed
  when: docker_py_version is version('2.6.0', '>=')
- assert:
    that:
      - dns_search_1 is failed
      - "'Minimum version required' in dns_search_1.msg"
  when: docker_py_version is version('2.6.0', '<')

####################################################################
## endpoint_mode ###################################################
####################################################################

- name: endpoint_mode
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    endpoint_mode: "dnsrr"
  register: endpoint_mode_1
  ignore_errors: true

- name: endpoint_mode (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    endpoint_mode: "dnsrr"
  register: endpoint_mode_2
  ignore_errors: true

- name: endpoint_mode (changes)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    endpoint_mode: "vip"
  register: endpoint_mode_3
  ignore_errors: true

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - endpoint_mode_1 is changed
      - endpoint_mode_2 is not changed
      - endpoint_mode_3 is changed
  when: docker_py_version is version('3.0.0', '>=')
- assert:
    that:
      - endpoint_mode_1 is failed
      - "'Minimum version required' in endpoint_mode_1.msg"
  when: docker_py_version is version('3.0.0', '<')

####################################################################
## env #############################################################
####################################################################

- name: env
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    env:
      - "TEST1=val1"
      - "TEST2=val2"
  register: env_1

- name: env (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    env:
      TEST1: val1
      TEST2: val2
  register: env_2

- name: env (changes)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    env:
      - "TEST1=val1"
      - "TEST2=val3"
  register: env_3

- name: env (order idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    env:
      - "TEST2=val3"
      - "TEST1=val1"
  register: env_4

- name: env (empty)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    env: []
  register: env_5

- name: env (empty idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    env: []
  register: env_6

- name: env (fail unwrapped values)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    env:
      TEST1: true
  register: env_7
  ignore_errors: true

- name: env (fail invalid formatted string)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    env:
      - "TEST1=val3"
      - "TEST2"
  register: env_8
  ignore_errors: true

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - env_1 is changed
      - env_2 is not changed
      - env_3 is changed
      - env_4 is not changed
      - env_5 is changed
      - env_6 is not changed
      - env_7 is failed
      - env_8 is failed

####################################################################
## env_files #######################################################
####################################################################

- name: Copy env-files
  copy:
    src: "{{ item }}"
    dest: "{{ remote_tmp_dir }}/{{ item }}"
  loop:
    - env-file-1
    - env-file-2

- name: env_files
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    env_files:
      - "{{ remote_tmp_dir }}/env-file-1"
  register: env_file_1

- name: env_files (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    env_files:
      - "{{ remote_tmp_dir }}/env-file-1"
  register: env_file_2

- name: env_files (more items)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    env_files:
      - "{{ remote_tmp_dir }}/env-file-1"
      - "{{ remote_tmp_dir }}/env-file-2"
  register: env_file_3

- name: env_files (order)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    env_files:
      - "{{ remote_tmp_dir }}/env-file-2"
      - "{{ remote_tmp_dir }}/env-file-1"
  register: env_file_4

- name: env_files (multiple idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    env_files:
      - "{{ remote_tmp_dir }}/env-file-2"
      - "{{ remote_tmp_dir }}/env-file-1"
  register: env_file_5

- name: env_files (empty)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    env_files: []
  register: env_file_6

- name: env_files (empty idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    env_files: []
  register: env_file_7

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - env_file_1 is changed
      - env_file_2 is not changed
      - env_file_3 is changed
      - env_file_4 is changed
      - env_file_5 is not changed
      - env_file_6 is changed
      - env_file_7 is not changed

###################################################################
## force_update ###################################################
###################################################################

- name: force_update
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    args:
      - sleep
      - "3600"
    force_update: true
  register: force_update_1
  ignore_errors: true

- name: force_update (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    args:
      - sleep
      - "3600"
    force_update: true
  register: force_update_2
  ignore_errors: true

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - force_update_1 is changed
      - force_update_2 is changed
  when: docker_py_version is version('2.1.0', '>=')
- assert:
    that:
      - force_update_1 is failed
      - "'Minimum version required' in force_update_1.msg"
  when: docker_py_version is version('2.1.0', '<')

####################################################################
## groups ##########################################################
####################################################################

- name: groups
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    groups:
      - "1234"
      - "5678"
  register: groups_1
  ignore_errors: true

- name: groups (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    groups:
      - "1234"
      - "5678"
  register: groups_2
  ignore_errors: true

- name: groups (order idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    groups:
      - "5678"
      - "1234"
  register: groups_3
  ignore_errors: true

- name: groups (change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    groups:
      - "1234"
  register: groups_4
  ignore_errors: true

- name: groups (empty)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    groups: []
  register: groups_5
  ignore_errors: true

- name: groups (empty idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    groups: []
  register: groups_6
  ignore_errors: true

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - groups_1 is changed
      - groups_2 is not changed
      - groups_3 is not changed
      - groups_4 is changed
      - groups_5 is changed
      - groups_6 is not changed
  when: docker_py_version is version('2.6.0', '>=')
- assert:
    that:
      - groups_1 is failed
      - "'Minimum version required' in groups_1.msg"
  when: docker_py_version is version('2.6.0', '<')

####################################################################
## healthcheck #####################################################
####################################################################

- name: healthcheck
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    healthcheck:
      test:
        - CMD
        - sleep
        - "1"
      timeout: 2s
      interval: 0h0m2s3ms4us
      retries: 2
      start_period: 20s
  register: healthcheck_1
  ignore_errors: true

- name: healthcheck (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    healthcheck:
      test:
        - CMD
        - sleep
        - 1
      timeout: 2s
      interval: 0h0m2s3ms4us
      retries: 2
      start_period: 20s
  register: healthcheck_2
  ignore_errors: true

- name: healthcheck (changed)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    healthcheck:
      test:
        - CMD
        - sleep
        - "1"
      timeout: 3s
      interval: 0h1m2s3ms4us
      retries: 3
  register: healthcheck_3
  ignore_errors: true

- name: healthcheck (disabled)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    healthcheck:
      test:
        - NONE
  register: healthcheck_4
  ignore_errors: true

- name: healthcheck (disabled, idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    healthcheck:
      test:
        - NONE
  register: healthcheck_5
  ignore_errors: true

- name: healthcheck (string in healthcheck test, changed)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    healthcheck:
      test: "sleep 1"
  register: healthcheck_6
  ignore_errors: true

- name: healthcheck (string in healthcheck test, idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    healthcheck:
      test: "sleep 1"
  register: healthcheck_7
  ignore_errors: true

- name: healthcheck (empty)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    healthcheck: {}
  register: healthcheck_8
  ignore_errors: true

- name: healthcheck (empty idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    healthcheck: {}
  register: healthcheck_9
  ignore_errors: true

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - healthcheck_1 is changed
      - healthcheck_2 is not changed
      - healthcheck_3 is changed
      - healthcheck_4 is changed
      - healthcheck_5 is not changed
      - healthcheck_6 is changed
      - healthcheck_7 is not changed
      - healthcheck_8 is changed
      - healthcheck_9 is not changed
  when: docker_api_version is version('1.29', '>=') and docker_py_version is version('2.6.0', '>=')
- assert:
    that:
      - healthcheck_1 is failed
      - "'Minimum version required' in healthcheck_1.msg"
  when: docker_api_version is version('1.29', '<') or docker_py_version is version('2.6.0', '<')

###################################################################
## hostname #######################################################
###################################################################

- name: hostname
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    hostname: me.example.com
  register: hostname_1
  ignore_errors: true

- name: hostname (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    hostname: me.example.com
  register: hostname_2
  ignore_errors: true

- name: hostname (change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    hostname: me.example.org
  register: hostname_3
  ignore_errors: true

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - hostname_1 is changed
      - hostname_2 is not changed
      - hostname_3 is changed
  when: docker_py_version is version('2.2.0', '>=')
- assert:
    that:
      - hostname_1 is failed
      - "'Minimum version required' in hostname_1.msg"
  when: docker_py_version is version('2.2.0', '<')

###################################################################
## hosts ##########################################################
###################################################################

- name: hosts
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    hosts:
      example.com: *******
      example.org: *******
  register: hosts_1
  ignore_errors: true

- name: hosts (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    hosts:
      example.com: *******
      example.org: *******
  register: hosts_2
  ignore_errors: true

- name: hosts (change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    hosts:
      example.com: *******
  register: hosts_3
  ignore_errors: true

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - hosts_1 is changed
      - hosts_2 is not changed
      - hosts_3 is changed
  when: docker_py_version is version('2.6.0', '>=')
- assert:
    that:
      - hosts_1 is failed
      - "'Minimum version required' in hosts_1.msg"
  when: docker_py_version is version('2.6.0', '<')


###################################################################
## image ##########################################################
###################################################################

- name: image
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
  register: image_1

- name: image (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
  register: image_2

- name: image (change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine_different }}"
  register: image_3

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - image_1 is changed
      - image_2 is not changed
      - image_3 is changed

####################################################################
## labels ##########################################################
####################################################################

- name: labels
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    labels:
      test_1: "1"
      test_2: "2"
  register: labels_1

- name: labels (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    labels:
      test_1: "1"
      test_2: "2"
  register: labels_2

- name: labels (changes)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    labels:
      test_1: "1"
      test_2: "2"
      test_3: "3"
  register: labels_3

- name: labels (empty)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    labels: {}
  register: labels_4

- name: labels (empty idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    labels: {}
  register: labels_5

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - labels_1 is changed
      - labels_2 is not changed
      - labels_3 is changed
      - labels_4 is changed
      - labels_5 is not changed

###################################################################
## mode ###########################################################
###################################################################

- name: mode
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    mode: "replicated"
    replicas: 1
  register: mode_1

- name: mode (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    mode: "replicated"
    replicas: 1
  register: mode_2

- name: mode (change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    mode: "global"
    replicas: 1
  register: mode_3

- name: mode (change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    mode: "replicated-job"
    replicas: 1
  register: mode_4
  ignore_errors: true

- name: mode (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    mode: "replicated-job"
    replicas: 1
  register: mode_5
  ignore_errors: true

- name: mode (change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    mode: "replicated"
    replicas: 1
  register: mode_6

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - mode_1 is changed
      - mode_2 is not changed
      - mode_3 is changed

- assert:
    that:
      - mode_4 is changed
      - mode_5 is not changed and mode_5 is not failed
      - mode_6 is changed
  when: docker_api_version is version('1.41', '>=') and docker_py_version is version('6.0.0', '>=')

- assert:
    that:
      - mode_4 is failed
      - "'Minimum version required' in mode_4.msg"
  when: docker_api_version is version('1.41', '<') or docker_py_version is version('6.0.0', '<')

####################################################################
## stop_grace_period ###############################################
####################################################################

- name: stop_grace_period
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    stop_grace_period: 60s
  register: stop_grace_period_1

- name: stop_grace_period (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    stop_grace_period: 60s
  register: stop_grace_period_2

- name: stop_grace_period (change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    stop_grace_period: 1m30s
  register: stop_grace_period_3

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - stop_grace_period_1 is changed
      - stop_grace_period_2 is not changed
      - stop_grace_period_3 is changed

####################################################################
## stop_signal #####################################################
####################################################################

- name: stop_signal
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    stop_signal: "30"
  register: stop_signal_1
  ignore_errors: true

- name: stop_signal (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    stop_signal: "30"
  register: stop_signal_2
  ignore_errors: true

- name: stop_signal (change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    stop_signal: "9"
  register: stop_signal_3
  ignore_errors: true

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - stop_signal_1 is changed
      - stop_signal_2 is not changed
      - stop_signal_3 is changed
  when: docker_api_version is version('1.28', '>=') and docker_py_version is version('2.6.0', '>=')
- assert:
    that:
      - stop_signal_1 is failed
      - "'Minimum version required' in stop_signal_1.msg"
  when: docker_api_version is version('1.28', '<') or docker_py_version is version('2.6.0', '<')

####################################################################
## publish #########################################################
####################################################################

- name: publish
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    publish:
      - protocol: tcp
        published_port: 60001
        target_port: 60001
      - protocol: udp
        published_port: 60002
        target_port: 60002
  register: publish_1
  ignore_errors: true

- name: publish (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    publish:
      - protocol: udp
        published_port: 60002
        target_port: 60002
      - published_port: 60001
        target_port: 60001
  register: publish_2
  ignore_errors: true

- name: publish (change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    publish:
      - protocol: tcp
        published_port: 60002
        target_port: 60003
      - protocol: udp
        published_port: 60001
        target_port: 60001
  register: publish_3
  ignore_errors: true

- name: publish (mode)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    publish:
      - protocol: tcp
        published_port: 60002
        target_port: 60003
        mode: host
      - protocol: udp
        published_port: 60001
        target_port: 60001
        mode: host
  register: publish_4
  ignore_errors: true

- name: publish (mode idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    publish:
      - protocol: udp
        published_port: 60001
        target_port: 60001
        mode: host
      - protocol: tcp
        published_port: 60002
        target_port: 60003
        mode: host
  register: publish_5
  ignore_errors: true

- name: publish (empty)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    publish: []
  register: publish_6
  ignore_errors: true

- name: publish (empty idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    publish: []
  register: publish_7
  ignore_errors: true

- name: publish (publishes the same port with both protocols)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    publish:
      - protocol: udp
        published_port: 60001
        target_port: 60001
        mode: host
      - protocol: tcp
        published_port: 60001
        target_port: 60001
        mode: host
  register: publish_8
  ignore_errors: true
- name: gather service info
  docker_swarm_service_info:
    name: "{{ service_name }}"
  register: publish_8_info

- name: publish (without published_port)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    publish:
      - protocol: udp
        target_port: 60001
        mode: host
  register: publish_9
  ignore_errors: true
- name: gather service info
  docker_swarm_service_info:
    name: "{{ service_name }}"
  register: publish_9_info

- name: publish (without published_port, idempotence)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    publish:
      - protocol: udp
        target_port: 60001
        mode: host
  register: publish_10
  ignore_errors: true
- name: gather service info
  docker_swarm_service_info:
    name: "{{ service_name }}"
  register: publish_10_info

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - publish_1 is changed
      - publish_2 is not changed
      - publish_3 is changed
      - publish_4 is changed
      - publish_5 is not changed
      - publish_6 is changed
      - publish_7 is not changed
      - publish_8 is changed
      - (publish_8_info.service.Endpoint.Ports | length) == 2
      - publish_9 is changed
      - publish_10 is not changed
  when: docker_py_version is version('3.0.0', '>=')
- assert:
    that:
      - publish_1 is failed
      - "'Minimum version required' in publish_1.msg"
  when: docker_py_version is version('3.0.0', '<')

###################################################################
## read_only ######################################################
###################################################################

- name: read_only
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    read_only: true
  register: read_only_1
  ignore_errors: true

- name: read_only (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    read_only: true
  register: read_only_2
  ignore_errors: true

- name: read_only (change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    read_only: false
  register: read_only_3
  ignore_errors: true

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - read_only_1 is changed
      - read_only_2 is not changed
      - read_only_3 is changed
  when: docker_api_version is version('1.28', '>=') and docker_py_version is version('2.6.0', '>=')
- assert:
    that:
      - read_only_1 is failed
      - "'Minimum version required' in read_only_1.msg"
  when: docker_api_version is version('1.28', '<') or docker_py_version is version('2.6.0', '<')

###################################################################
## replicas #######################################################
###################################################################

- name: replicas
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    replicas: 2
  register: replicas_1

- name: replicas (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    replicas: 2
  register: replicas_2

- name: replicas (change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    replicas: 3
  register: replicas_3

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - replicas_1 is changed
      - replicas_2 is not changed
      - replicas_3 is changed

###################################################################
# resolve_image ###################################################
###################################################################

- name: resolve_image (false)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    command: '/bin/sh -v -c "sleep 10m"'
    resolve_image: false
  register: resolve_image_1

- name: resolve_image (false idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    command: '/bin/sh -v -c "sleep 10m"'
    resolve_image: false
  register: resolve_image_2

- name: resolve_image (change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    command: '/bin/sh -v -c "sleep 10m"'
    resolve_image: true
  register: resolve_image_3
  ignore_errors: true

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - resolve_image_1 is changed
      - resolve_image_2 is not changed
      - resolve_image_3 is changed
  when: docker_api_version is version('1.30', '>=') and docker_py_version is version('3.2.0', '>=')
- assert:
    that:
      - resolve_image_1 is changed
      - resolve_image_2 is not changed
      - resolve_image_3 is failed
      - "('version is ' ~ docker_py_version ~ ' ') in resolve_image_3.msg"
      - "'Minimum version required is 3.2.0 ' in resolve_image_3.msg"
  when: docker_api_version is version('1.30', '<') or docker_py_version is version('3.2.0', '<')

###################################################################
# tty #############################################################
###################################################################

- name: tty
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    tty: true
  register: tty_1
  ignore_errors: true

- name: tty (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    tty: true
  register: tty_2
  ignore_errors: true

- name: tty (change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    tty: false
  register: tty_3
  ignore_errors: true

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - tty_1 is changed
      - tty_2 is not changed
      - tty_3 is changed
  when: docker_py_version is version('2.4.0', '>=')
- assert:
    that:
      - tty_1 is failed
      - "'Minimum version required' in tty_1.msg"
  when: docker_py_version is version('2.4.0', '<')

###################################################################
## user ###########################################################
###################################################################

- name: user
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    user: "operator"
  register: user_1

- name: user (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    user: "operator"
  register: user_2

- name: user (change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    user: "root"
  register: user_3

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - user_1 is changed
      - user_2 is not changed
      - user_3 is changed

####################################################################
## working_dir #####################################################
####################################################################

- name: working_dir
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    working_dir: /tmp
  register: working_dir_1

- name: working_dir (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    working_dir: /tmp
  register: working_dir_2

- name: working_dir (change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    working_dir: /
  register: working_dir_3

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - working_dir_1 is changed
      - working_dir_2 is not changed
      - working_dir_3 is changed

####################################################################
## init ############################################################
####################################################################

- name: init
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    init: true
  register: init_1

- name: init (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    init: true
  register: init_2

- name: init (change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    init: false
  register: init_3

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - init_1 is changed
      - init_2 is not changed
      - init_3 is changed
  when: docker_api_version is version('1.37', '>=')

- assert:
    that:
      - init_1 is failed
      - "('version is ' ~ docker_api_version ~'. Minimum version required is 1.37') in hosts_1.msg"
  when: docker_api_version is version('1.37', '<')

####################################################################
## cap_drop, capabilities ##########################################
####################################################################

- name: capabilities, cap_drop
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    init: true
    cap_add:
      - sys_time
    cap_drop:
      - all
  register: capabilities_1
  ignore_errors: true

- name: capabilities, cap_drop (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    init: true
    cap_add:
      - sys_time
    cap_drop:
      - all
  register: capabilities_2
  ignore_errors: true
  diff: true

- name: capabilities, cap_drop (less)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    init: true
    cap_add: []
    cap_drop:
      - all
  register: capabilities_3
  ignore_errors: true
  diff: true

- name: capabilities, cap_drop (changed)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    init: true
    cap_add:
      - setgid
    cap_drop:
      - all
  register: capabilities_4
  ignore_errors: true
  diff: true

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - capabilities_1 is changed
      - capabilities_2 is not changed
      - capabilities_3 is changed
      - capabilities_4 is changed
  when: docker_api_version is version('1.41', '>=') and docker_py_version is version('5.0.3', '>=')

- assert:
    that:
      - capabilities_1 is failed
      - >
        (('version is ' ~ docker_py_version ~ ' ') in capabilities_1.msg and 'Minimum version required is 5.0.3 ' in capabilities_1.msg)
        or (('Docker API version is ' ~ docker_api_version ~ '. ') in capabilities_1.msg and 'Minimum version required is 1.41 ' in capabilities_1.msg)
  when: docker_api_version is version('1.41', '<') or docker_py_version is version('5.0.3', '<')
