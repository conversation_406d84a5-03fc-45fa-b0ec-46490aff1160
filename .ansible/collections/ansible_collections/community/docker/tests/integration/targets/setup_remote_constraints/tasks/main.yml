---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

####################################################################
# WARNING: These are designed specifically for Ansible tests       #
# and should not be used as examples of how to write Ansible roles #
####################################################################

- name: record constraints.txt path on remote host
  set_fact:
    remote_constraints: "{{ remote_tmp_dir }}/constraints.txt"

- name: copy constraints.txt to remote host
  copy:
    src: "{{ role_path }}/../../../utils/constraints.txt"
    dest: "{{ remote_constraints }}"
