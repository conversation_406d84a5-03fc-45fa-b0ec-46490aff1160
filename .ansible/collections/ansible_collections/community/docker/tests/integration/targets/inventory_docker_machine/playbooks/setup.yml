---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- hosts: 127.0.0.1
  connection: local
  tasks:
    - name: Request Docker Machine to use this machine as a generic VM
      command: "docker-machine --debug create \
        --driver generic \
        --generic-ip-address=localhost \
        --generic-ssh-key {{ lookup('env', 'HOME') }}/.ssh/id_rsa \
        --generic-ssh-user root \
        vm"
