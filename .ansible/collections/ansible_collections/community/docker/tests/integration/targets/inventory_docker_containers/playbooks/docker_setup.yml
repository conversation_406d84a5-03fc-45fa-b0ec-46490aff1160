---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- hosts: 127.0.0.1
  connection: local
  vars:
    docker_skip_cleanup: true

  tasks:
    - name: Setup docker
      import_role:
        name: setup_docker

    - name: Setup Docker Python deps
      import_role:
        name: setup_docker_python_deps

    - name: Start containers
      docker_container:
        name: "{{ item.name }}"
        image: "{{ docker_test_image_alpine }}"
        state: started
        command: '/bin/sh -c "sleep 10m"'
        published_ports:
          - 22/tcp
        labels:
          foo: !unsafe 'EVALU{{ "" }}ATED'
      loop:
        - name: ansible-docker-test-docker-inventory-container-1
        - name: ansible-docker-test-docker-inventory-container-2
