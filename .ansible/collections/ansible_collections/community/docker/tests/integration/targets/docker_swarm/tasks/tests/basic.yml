---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- debug:
    msg: Running tests/basic.yml

####################################################################
## Errors ##########################################################
####################################################################
- name: Test parameters with state=join
  docker_swarm:
    state: join
  ignore_errors: true
  register: output

- name: assert failure when called with state=join and no remote_addrs,join_token
  assert:
    that:
      - 'output.failed'
      - 'output.msg == "state is join but all of the following are missing: remote_addrs, join_token"'

- name: Test parameters with state=remove
  docker_swarm:
    state: remove
  ignore_errors: true
  register: output

- name: assert failure when called with state=remove and no node_id
  assert:
    that:
      - 'output.failed'
      - 'output.msg == "state is remove but all of the following are missing: node_id"'

####################################################################
## Creation ########################################################
####################################################################

- name: Create a Swarm cluster (check mode)
  docker_swarm:
    state: present
    advertise_addr: "{{ansible_default_ipv4.address | default('127.0.0.1')}}"
  check_mode: true
  diff: true
  register: output_1

- name: Create a Swarm cluster
  docker_swarm:
    state: present
    advertise_addr: "{{ansible_default_ipv4.address | default('127.0.0.1')}}"
  diff: true
  register: output_2

- name: Create a Swarm cluster (idempotent)
  docker_swarm:
    state: present
    advertise_addr: "{{ansible_default_ipv4.address | default('127.0.0.1')}}"
  diff: true
  register: output_3

- name: Create a Swarm cluster (idempotent, check mode)
  docker_swarm:
    state: present
    advertise_addr: "{{ansible_default_ipv4.address | default('127.0.0.1')}}"
  check_mode: true
  diff: true
  register: output_4

- name: Create a Swarm cluster (force re-create)
  docker_swarm:
    state: present
    advertise_addr: "{{ansible_default_ipv4.address | default('127.0.0.1')}}"
    force: true
  diff: true
  register: output_5

- name: Create a Swarm cluster (force re-create, check mode)
  docker_swarm:
    state: present
    advertise_addr: "{{ansible_default_ipv4.address | default('127.0.0.1')}}"
    force: true
  check_mode: true
  diff: true
  register: output_6

- name: assert changed when create a new swarm cluster
  assert:
    that:
      - 'output_1 is changed'
      - 'output_1.diff.before is defined'
      - 'output_1.diff.after is defined'
      - 'output_2 is changed'
      - '(output_2.actions[0] | regex_search("New Swarm cluster created: ")) is truthy'
      - 'output_2.swarm_facts.JoinTokens.Manager is truthy'
      - 'output_2.swarm_facts.JoinTokens.Worker is truthy'
      - 'output_2.diff.before is defined'
      - 'output_2.diff.after is defined'
      - 'output_3 is not changed'
      - 'output_3.diff.before is defined'
      - 'output_3.diff.after is defined'
      - 'output_4 is not changed'
      - 'output_4.diff.before is defined'
      - 'output_4.diff.after is defined'
      - 'output_5 is changed'
      - 'output_5.diff.before is defined'
      - 'output_5.diff.after is defined'
      - 'output_6 is changed'
      - 'output_6.diff.before is defined'
      - 'output_6.diff.after is defined'

####################################################################
## Removal #########################################################
####################################################################

- name: Remove a Swarm cluster (check mode)
  docker_swarm:
    state: absent
    force: true
  check_mode: true
  diff: true
  register: output_1

- name: Remove a Swarm cluster
  docker_swarm:
    state: absent
    force: true
  diff: true
  register: output_2

- name: Remove a Swarm cluster (idempotent)
  docker_swarm:
    state: absent
    force: true
  diff: true
  register: output_3

- name: Remove a Swarm cluster (idempotent, check mode)
  docker_swarm:
    state: absent
    force: true
  check_mode: true
  diff: true
  register: output_4

- name: assert changed when remove a swarm cluster
  assert:
    that:
      - 'output_1 is changed'
      - 'output_1.diff.before is defined'
      - 'output_1.diff.after is defined'
      - 'output_2 is changed'
      - 'output_2.actions[0] == "Node has left the swarm cluster"'
      - 'output_2.diff.before is defined'
      - 'output_2.diff.after is defined'
      - 'output_3 is not changed'
      - 'output_3.diff.before is defined'
      - 'output_3.diff.after is defined'
      - 'output_4 is not changed'
      - 'output_4.diff.before is defined'
      - 'output_4.diff.after is defined'

- include_tasks: cleanup.yml
