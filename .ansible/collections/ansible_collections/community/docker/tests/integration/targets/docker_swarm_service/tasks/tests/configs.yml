---
# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

- name: Registering container name
  set_fact:
    service_name: "{{ name_prefix ~ '-configs' }}"
    config_name_1: "{{ name_prefix ~ '-configs-1' }}"
    config_name_2: "{{ name_prefix ~ '-configs-2' }}"
    config_name_3: "{{ name_prefix ~ '-configs-3' }}"

- name: Registering container name
  set_fact:
    config_names: "{{ config_names + [config_name_1, config_name_2] }}"

- docker_config:
    name: "{{ config_name_1 }}"
    data: "hello"
    state: present
  register: "config_result_1"
  when: docker_api_version is version('1.30', '>=') and docker_py_version is version('2.6.0', '>=')

- docker_config:
    name: "{{ config_name_2 }}"
    data: "test"
    state: present
  register: "config_result_2"
  when: docker_api_version is version('1.30', '>=') and docker_py_version is version('2.6.0', '>=')

- docker_config:
    name: "{{ config_name_3 }}"
    data: "config3"
    state: present
    rolling_versions: true
  register: "config_result_3"
  when: docker_api_version is version('1.30', '>=') and docker_py_version is version('2.6.0', '>=')

####################################################################
## configs #########################################################
####################################################################

- name: configs
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    configs:
      - config_id: "{{ config_result_1.config_id|default('') }}"
        config_name: "{{ config_name_1 }}"
        filename: "/tmp/{{ config_name_1 }}.txt"
  register: configs_1
  ignore_errors: true

- name: configs (idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    configs:
      - config_name: "{{ config_name_1 }}"
        filename: "/tmp/{{ config_name_1 }}.txt"
  register: configs_2
  ignore_errors: true

- name: configs (add)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    configs:
      - config_id: "{{ config_result_1.config_id|default('') }}"
        config_name: "{{ config_name_1 }}"
        filename: "/tmp/{{ config_name_1 }}.txt"
      - config_name: "{{ config_name_2 }}"
        filename: "/tmp/{{ config_name_2 }}.txt"
  register: configs_3
  ignore_errors: true

- name: configs (add idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    configs:
      - config_name: "{{ config_name_1 }}"
        filename: "/tmp/{{ config_name_1 }}.txt"
      - config_id: "{{ config_result_2.config_id|default('') }}"
        config_name: "{{ config_name_2 }}"
        filename: "/tmp/{{ config_name_2 }}.txt"
  register: configs_4
  ignore_errors: true

- name: configs (add idempotency no id)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    configs:
      - config_name: "{{ config_name_1 }}"
        filename: "/tmp/{{ config_name_1 }}.txt"
      - config_name: "{{ config_name_2 }}"
        filename: "/tmp/{{ config_name_2 }}.txt"
  register: configs_5
  ignore_errors: true

- name: configs (add idempotency no id and re-ordered)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    configs:
      - config_name: "{{ config_name_2 }}"
        filename: "/tmp/{{ config_name_2 }}.txt"
      - config_name: "{{ config_name_1 }}"
        filename: "/tmp/{{ config_name_1 }}.txt"
  register: configs_6
  ignore_errors: true

- name: configs (empty)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    configs: []
  register: configs_7
  ignore_errors: true

- name: configs (empty idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    configs: []
  register: configs_8
  ignore_errors: true

- name: rolling configs
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    configs:
      - config_name: "{{ config_name_3 }}_v1"
        filename: "/run/configs/{{ config_name_3 }}.txt"
  register: configs_9
  ignore_errors: true

- name: update rolling config
  docker_config:
    name: "{{ config_name_3 }}"
    data: "newconfig3"
    state: "present"
    rolling_versions: true
  register: configs_10
  when: docker_api_version is version('1.30', '>=') and docker_py_version is version('2.6.0', '>=')
  ignore_errors: true

- name: rolling configs service update
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    configs:
      - config_name: "{{ config_name_3 }}_v2"
        filename: "/run/configs/{{ config_name_3 }}.txt"
  register: configs_11
  ignore_errors: true

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false

- assert:
    that:
      - configs_1 is changed
      - configs_2 is not changed
      - configs_3 is changed
      - configs_4 is not changed
      - configs_5 is not changed
      - configs_6 is not changed
      - configs_7 is changed
      - configs_8 is not changed
      - configs_9 is changed
      - configs_10 is not failed
      - configs_11 is changed
  when: docker_api_version is version('1.30', '>=') and docker_py_version is version('2.6.0', '>=')

- assert:
    that:
      - configs_1 is failed
      - "'Minimum version required' in configs_1.msg"
  when: docker_api_version is version('1.30', '<') or docker_py_version is version('2.6.0', '<')

####################################################################
## configs (uid) ###################################################
####################################################################

- name: configs (uid int)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    configs:
      - config_id: "{{ config_result_1.config_id|default('') }}"
        config_name: "{{ config_name_1 }}"
        uid: 1000
  register: configs_1
  ignore_errors: true

- name: configs (uid int idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    configs:
      - config_id: "{{ config_result_1.config_id|default('') }}"
        config_name: "{{ config_name_1 }}"
        uid: 1000
  register: configs_2
  ignore_errors: true

- name: configs (uid int change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    configs:
      - config_id: "{{ config_result_1.config_id|default('') }}"
        config_name: "{{ config_name_1 }}"
        uid: 1002
  register: configs_3
  ignore_errors: true

- name: configs (uid str)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    configs:
      - config_id: "{{ config_result_1.config_id|default('') }}"
        config_name: "{{ config_name_1 }}"
        uid: "1001"
  register: configs_4
  ignore_errors: true

- name: configs (uid str idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    configs:
      - config_id: "{{ config_result_1.config_id|default('') }}"
        config_name: "{{ config_name_1 }}"
        uid: "1001"
  register: configs_5
  ignore_errors: true

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false
- assert:
    that:
      - configs_1 is changed
      - configs_2 is not changed
      - configs_3 is changed
      - configs_4 is changed
      - configs_5 is not changed
  when: docker_api_version is version('1.30', '>=') and docker_py_version is version('2.6.0', '>=')

- assert:
    that:
      - configs_1 is failed
      - "'Minimum version required' in configs_1.msg"
  when: docker_api_version is version('1.30', '<') or docker_py_version is version('2.6.0', '<')


####################################################################
## configs (gid) ###################################################
####################################################################

- name: configs (gid int)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    configs:
      - config_id: "{{ config_result_1.config_id|default('') }}"
        config_name: "{{ config_name_1 }}"
        gid: 1000
  register: configs_1
  ignore_errors: true

- name: configs (gid int idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    configs:
      - config_id: "{{ config_result_1.config_id|default('') }}"
        config_name: "{{ config_name_1 }}"
        gid: 1000
  register: configs_2
  ignore_errors: true

- name: configs (gid int change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    configs:
      - config_id: "{{ config_result_1.config_id|default('') }}"
        config_name: "{{ config_name_1 }}"
        gid: 1002
  register: configs_3
  ignore_errors: true

- name: configs (gid str)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    configs:
      - config_id: "{{ config_result_1.config_id|default('') }}"
        config_name: "{{ config_name_1 }}"
        gid: "1001"
  register: configs_4
  ignore_errors: true

- name: configs (gid str idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    configs:
      - config_id: "{{ config_result_1.config_id|default('') }}"
        config_name: "{{ config_name_1 }}"
        gid: "1001"
  register: configs_5
  ignore_errors: true

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false
- assert:
    that:
      - configs_1 is changed
      - configs_2 is not changed
      - configs_3 is changed
      - configs_4 is changed
      - configs_5 is not changed
  when: docker_api_version is version('1.30', '>=') and docker_py_version is version('2.6.0', '>=')

- assert:
    that:
      - configs_1 is failed
      - "'Minimum version required' in configs_1.msg"
  when: docker_api_version is version('1.30', '<') or docker_py_version is version('2.6.0', '<')

####################################################################
## configs (mode) ##################################################
####################################################################

- name: configs (mode)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    configs:
      - config_id: "{{ config_result_1.config_id|default('') }}"
        config_name: "{{ config_name_1 }}"
        mode: "0600"
  register: configs_1
  ignore_errors: true

- name: configs (mode idempotency)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    configs:
      - config_id: "{{ config_result_1.config_id|default('') }}"
        config_name: "{{ config_name_1 }}"
        mode: "0600"
  register: configs_2
  ignore_errors: true

- name: configs (mode change)
  docker_swarm_service:
    name: "{{ service_name }}"
    image: "{{ docker_test_image_alpine }}"
    resolve_image: false
    command: '/bin/sh -v -c "sleep 10m"'
    configs:
      - config_id: "{{ config_result_1.config_id|default('') }}"
        config_name: "{{ config_name_1 }}"
        mode: "0777"
  register: configs_3
  ignore_errors: true

- name: cleanup
  docker_swarm_service:
    name: "{{ service_name }}"
    state: absent
  diff: false
- assert:
    that:
      - configs_1 is changed
      - configs_2 is not changed
      - configs_3 is changed
  when: docker_api_version is version('1.30', '>=') and docker_py_version is version('2.6.0', '>=')

- assert:
    that:
      - configs_1 is failed
      - "'Minimum version required' in configs_1.msg"
  when: docker_api_version is version('1.30', '<') or docker_py_version is version('2.6.0', '<')

####################################################################
####################################################################
####################################################################

- name: Delete configs
  docker_config:
    name: "{{ config_name }}"
    state: absent
    force: true
  loop:
    - "{{ config_name_1 }}"
    - "{{ config_name_2 }}"
    - "{{ config_name_3 }}"
  loop_control:
    loop_var: config_name
  ignore_errors: true
  when: docker_api_version is version('1.30', '>=') and docker_py_version is version('2.6.0', '>=')
