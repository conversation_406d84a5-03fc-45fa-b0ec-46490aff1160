# Copyright (c) Ansible Project
# GNU General Public License v3.0+ (see LICENSES/GPL-3.0-or-later.txt or https://www.gnu.org/licenses/gpl-3.0.txt)
# SPDX-License-Identifier: GPL-3.0-or-later

"""Unit tests for docker_network."""

from __future__ import (absolute_import, division, print_function)
__metaclass__ = type

import pytest

from ansible_collections.community.docker.plugins.modules.docker_network import validate_cidr


@pytest.mark.parametrize("cidr,expected", [
    ('***********/16', 'ipv4'),
    ('***********/24', 'ipv4'),
    ('***********/32', 'ipv4'),
    ('fdd1:ac8c:0557:7ce2::/64', 'ipv6'),
    ('fdd1:ac8c:0557:7ce2::/128', 'ipv6'),
])
def test_validate_cidr_positives(cidr, expected):
    assert validate_cidr(cidr) == expected


@pytest.mark.parametrize("cidr", [
    '***********',
    '***********/34',
    '***********/asd',
    'fdd1:ac8c:0557:7ce2::',
])
def test_validate_cidr_negatives(cidr):
    with pytest.raises(ValueError) as e:
        validate_cidr(cidr)
    assert '"{0}" is not a valid CIDR'.format(cidr) == str(e.value)
