# -*- coding: utf-8 -*-
# This code is part of the Ansible collection community.docker, but is an independent component.
# This particular file, and this file only, is based on the Docker SDK for Python (https://github.com/docker/docker-py/)
#
# Copyright (c) 2016-2022 Docker, Inc.
#
# It is licensed under the Apache 2.0 license (see LICENSES/Apache-2.0.txt in this collection)
# SPDX-License-Identifier: Apache-2.0

from __future__ import (absolute_import, division, print_function)
__metaclass__ = type

OBJ = {
    "read": "2015-02-11T19:20:46.667237763+02:00",
    "network": {
        "rx_bytes": 567224,
        "rx_packets": 3773,
        "rx_errors": 0,
        "rx_dropped": 0,
        "tx_bytes": 1176,
        "tx_packets": 13,
        "tx_errors": 0,
        "tx_dropped": 0
    },
    "cpu_stats": {
        "cpu_usage": {
            "total_usage": 157260874053,
            "percpu_usage": [
                52196306950,
                24118413549,
                53292684398,
                27653469156
            ],
            "usage_in_kernelmode": 37140000000,
            "usage_in_usermode": 62140000000
        },
        "system_cpu_usage": 3.0881377e+14,
        "throttling_data": {
            "periods": 0,
            "throttled_periods": 0,
            "throttled_time": 0
        }
    },
    "memory_stats": {
        "usage": 179314688,
        "max_usage": 258166784,
        "stats": {
            "active_anon": 90804224,
            "active_file": 2195456,
            "cache": 3096576,
            "hierarchical_memory_limit": 1.844674407371e+19,
            "inactive_anon": 85516288,
            "inactive_file": 798720,
            "mapped_file": 2646016,
            "pgfault": 101034,
            "pgmajfault": 1207,
            "pgpgin": 115814,
            "pgpgout": 75613,
            "rss": 176218112,
            "rss_huge": 12582912,
            "total_active_anon": 90804224,
            "total_active_file": 2195456,
            "total_cache": 3096576,
            "total_inactive_anon": 85516288,
            "total_inactive_file": 798720,
            "total_mapped_file": 2646016,
            "total_pgfault": 101034,
            "total_pgmajfault": 1207,
            "total_pgpgin": 115814,
            "total_pgpgout": 75613,
            "total_rss": 176218112,
            "total_rss_huge": 12582912,
            "total_unevictable": 0,
            "total_writeback": 0,
            "unevictable": 0,
            "writeback": 0
        },
        "failcnt": 0,
        "limit": 8039038976
    },
    "blkio_stats": {
        "io_service_bytes_recursive": [
            {
                "major": 8,
                "minor": 0,
                "op": "Read",
                "value": 72843264
            }, {
                "major": 8,
                "minor": 0,
                "op": "Write",
                "value": 4096
            }, {
                "major": 8,
                "minor": 0,
                "op": "Sync",
                "value": 4096
            }, {
                "major": 8,
                "minor": 0,
                "op": "Async",
                "value": 72843264
            }, {
                "major": 8,
                "minor": 0,
                "op": "Total",
                "value": 72847360
            }
        ],
        "io_serviced_recursive": [
            {
                "major": 8,
                "minor": 0,
                "op": "Read",
                "value": 10581
            }, {
                "major": 8,
                "minor": 0,
                "op": "Write",
                "value": 1
            }, {
                "major": 8,
                "minor": 0,
                "op": "Sync",
                "value": 1
            }, {
                "major": 8,
                "minor": 0,
                "op": "Async",
                "value": 10581
            }, {
                "major": 8,
                "minor": 0,
                "op": "Total",
                "value": 10582
            }
        ],
        "io_queue_recursive": [],
        "io_service_time_recursive": [],
        "io_wait_time_recursive": [],
        "io_merged_recursive": [],
        "io_time_recursive": [],
        "sectors_recursive": []
    }
}
